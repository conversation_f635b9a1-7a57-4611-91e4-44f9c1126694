import React, { useState, useEffect, useCallback, memo, useRef } from "react";
import Image from "next/image";
import { signOut } from "next-auth/react";
import { Line, Pie, Doughnut, Bar } from 'react-chartjs-2';
import { Chart as ChartJS } from 'chart.js';
import { Plus } from 'lucide-react';

import KpiSemanalModal from './KpiSemanalModal';
import KpiSemanalInlineForm from './KpiSemanalInlineForm';
import KpiHistorySection from './KpiHistorySection';
import ConfirmDialog from './ui/ConfirmDialog';
import AdminDashboardCompras from './AdminDashboardCompras';
import KpiLogisticaModal from './KpiLogisticaModal';
import KpiLogisticaInlineForm from './KpiLogisticaInlineForm';
import { getKpisSemanales, createKpiSemanal, updateKpiSemanal, deleteKpiSemanal, type KpiSemanalData } from '@/app/actions/kpis-semanales';
import { getKpisCompras, type KpiComprasData } from '@/app/actions/kpis-compras';
import { getKpisLogistica, createKpiLogistica, updateKpiLogistica, deleteKpiLogistica, type KpiLogisticaData } from '@/app/actions/kpis-logistica';
import { formatWeekDatesForChart } from '@/lib/utils/weekUtils';
import jsPDF from 'jspdf';
import {
  ChartExportUtils,
  SemicircleGauge,
  getSmartDataLabelsConfig,
  getDoughnutDataLabelsConfig,
  getChartLayout,
  getExpandedChartScalesConfig,
  getStackedChartScalesConfig,
  getDoughnutChartOptions
} from './AdminDashboardUtils';


import {
  Users,
  TrendingUp,
  DollarSign,
  Activity,
  Droplet,
  Menu,
  X,
  ShoppingCart,
  Package,
  Truck,
  Calculator,
  Settings,
  Scale,
  Target,
  Banknote,
  Info,
  Eye,
  Filter,
  Trash2
} from "lucide-react";

import appLogo from '@/assets/images/logo-combustibles-cassiopeia.png';

interface AdminDashboardProps {
  user: {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role?: string;
  };
}

interface DashboardData {
  precios: any[];
  terminales: any[];
  leads: any[];
  tarifas: any[];
}

const AdminDashboard = ({ user }: AdminDashboardProps) => {

  // Datos de ejemplo para el dashboard
  const mockData = {
    preciosRecientes: [
      { fecha: "2024-01", precio: 22.50, terminal: "Terminal A" },
      { fecha: "2024-02", precio: 23.10, terminal: "Terminal A" },
      { fecha: "2024-03", precio: 22.80, terminal: "Terminal A" },
      { fecha: "2024-04", precio: 24.20, terminal: "Terminal A" },
      { fecha: "2024-05", precio: 23.90, terminal: "Terminal A" },
      { fecha: "2024-06", precio: 25.10, terminal: "Terminal A" },
    ],
    ventasPorTerminal: [
      { terminal: "Terminal Norte", ventas: 1250, color: "#8884d8" },
      { terminal: "Terminal Sur", ventas: 980, color: "#82ca9d" },
      { terminal: "Terminal Este", ventas: 1100, color: "#ffc658" },
      { terminal: "Terminal Oeste", ventas: 850, color: "#ff7300" },
    ],
    leadsConversion: [
      { mes: "Ene", leads: 120, conversiones: 45 },
      { mes: "Feb", leads: 150, conversiones: 62 },
      { mes: "Mar", leads: 180, conversiones: 78 },
      { mes: "Abr", leads: 200, conversiones: 85 },
      { mes: "May", leads: 165, conversiones: 71 },
      { mes: "Jun", leads: 190, conversiones: 89 },
    ],
    estadisticasGenerales: {
      totalUsuarios: 1247,
      totalLeads: 3456,
      precioPromedio: 23.85,
      terminalesActivas: 12
    },
    indicadoresVentas: {
      volumenTotalLitros: 2450000,
      crecimientoMensual: 8.5,
      margenBrutoPorLitro: 2.35,
      tasaRetencionClientes: 87.2,
      cumplimientoObjetivo: 94.8,
      desviacionVentas: -5.2,
      cicloPromedioCierre: 12,
      clientesActivosMensuales: 1847
    },
    evolucionIndicadores: [
      { mes: "Ene", volumen: 2200000, crecimiento: 5.2, margen: 2.15, retencion: 85.5, cumplimiento: 92.3, desviacion: -3.1, ciclo: 14, clientesActivos: 1650 },
      { mes: "Feb", volumen: 2350000, crecimiento: 6.8, margen: 2.25, retencion: 86.2, cumplimiento: 93.1, desviacion: -2.8, ciclo: 13, clientesActivos: 1720 },
      { mes: "Mar", volumen: 2280000, crecimiento: -3.0, margen: 2.18, retencion: 84.8, cumplimiento: 91.7, desviacion: -4.2, ciclo: 15, clientesActivos: 1680 },
      { mes: "Abr", volumen: 2420000, crecimiento: 6.1, margen: 2.32, retencion: 87.1, cumplimiento: 94.2, desviacion: -1.9, ciclo: 11, clientesActivos: 1780 },
      { mes: "May", volumen: 2380000, crecimiento: -1.7, margen: 2.28, retencion: 86.8, cumplimiento: 93.8, desviacion: -3.5, ciclo: 13, clientesActivos: 1820 },
      { mes: "Jun", volumen: 2450000, crecimiento: 2.9, margen: 2.35, retencion: 87.2, cumplimiento: 94.8, desviacion: -2.1, ciclo: 12, clientesActivos: 1847 },
    ],
    distribucionCumplimiento: [
      { name: "Cumplido", value: 94.8, color: "#10b981" },
      { name: "Pendiente", value: 5.2, color: "#f59e0b" }
    ],
    distribucionRetencion: [
      { name: "Clientes Retenidos", value: 87.2, color: "#3b82f6" },
      { name: "Clientes Perdidos", value: 12.8, color: "#ef4444" }
    ],
    // Datos mock para el panel de Compras
    indicadoresCompras: {
      numeroProveedoresActivos: 24,
      porcentajeReporteGanancia: 92.5,
      preciosPromedioCompra: 21.85,
      diferencialPrecioPemex: -1.25,
      porcentajeCompraPorProveedor: [
        { proveedor: "Proveedor A", porcentaje: 35.2, color: "#3b82f6" },
        { proveedor: "Proveedor B", porcentaje: 28.7, color: "#10b981" },
        { proveedor: "Proveedor C", porcentaje: 18.5, color: "#f59e0b" },
        { proveedor: "Proveedor D", porcentaje: 12.3, color: "#8b5cf6" },
        { proveedor: "Otros", porcentaje: 5.3, color: "#ef4444" }
      ]
    },
    evolucionIndicadoresCompras: [
      { mes: "Ene", proveedores: 22, reporteGanancia: 89.2, precioPromedio: 21.45, diferencial: -1.15, porcentajeA: 32.1, porcentajeB: 30.2, porcentajeC: 20.1, porcentajeD: 12.6, porcentajeOtros: 5.0 },
      { mes: "Feb", proveedores: 23, reporteGanancia: 91.1, precioPromedio: 21.62, diferencial: -1.18, porcentajeA: 33.5, porcentajeB: 29.8, porcentajeC: 19.2, porcentajeD: 12.1, porcentajeOtros: 5.4 },
      { mes: "Mar", proveedores: 21, reporteGanancia: 88.7, precioPromedio: 21.38, diferencial: -1.32, porcentajeA: 31.8, porcentajeB: 31.1, porcentajeC: 19.8, porcentajeD: 12.8, porcentajeOtros: 4.5 },
      { mes: "Abr", proveedores: 24, reporteGanancia: 93.2, precioPromedio: 21.78, diferencial: -1.22, porcentajeA: 34.2, porcentajeB: 28.5, porcentajeC: 18.9, porcentajeD: 12.7, porcentajeOtros: 5.7 },
      { mes: "May", proveedores: 23, reporteGanancia: 90.8, precioPromedio: 21.71, diferencial: -1.28, porcentajeA: 33.8, porcentajeB: 29.1, porcentajeC: 18.2, porcentajeD: 13.1, porcentajeOtros: 5.8 },
      { mes: "Jun", proveedores: 24, reporteGanancia: 92.5, precioPromedio: 21.85, diferencial: -1.25, porcentajeA: 35.2, porcentajeB: 28.7, porcentajeC: 18.5, porcentajeD: 12.3, porcentajeOtros: 5.3 },
    ]
  };

  // Estados del componente
  const [activeTab, setActiveTab] = useState("ventas");
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    precios: [],
    terminales: [],
    leads: [],
    tarifas: []
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState("4w");
  const [isKpiModalOpen, setIsKpiModalOpen] = useState(false);
  const [kpisSemanales, setKpisSemanales] = useState<any[]>([]);
  const [loadingKpis, setLoadingKpis] = useState(false);
  const [hasRealData, setHasRealData] = useState(false);
  const [indicadoresActuales, setIndicadoresActuales] = useState(mockData.indicadoresVentas);
  const [evolucionActual, setEvolucionActual] = useState(mockData.evolucionIndicadores);
  const [distribucionCumplimientoActual, setDistribucionCumplimientoActual] = useState(mockData.distribucionCumplimiento);
  const [distribucionRetencionActual, setDistribucionRetencionActual] = useState(mockData.distribucionRetencion);

  const [editingKpi, setEditingKpi] = useState<KpiSemanalData | null>(null);

  // Estados para KPIs de compras (para cargar en background)
  const [kpisCompras, setKpisCompras] = useState<any[]>([]);
  const [loadingKpisCompras, setLoadingKpisCompras] = useState(false);

  // Estados para KPIs de logística
  const [kpisLogistica, setKpisLogistica] = useState<any[]>([]);
  const [loadingKpisLogistica, setLoadingKpisLogistica] = useState(false);
  const [editingKpiLogistica, setEditingKpiLogistica] = useState<KpiLogisticaData | null>(null);
  const [showKpiLogisticaModal, setShowKpiLogisticaModal] = useState(false);
  const [showInlineFormLogistica, setShowInlineFormLogistica] = useState(false);
  const [isAddingOldWeekLogistica, setIsAddingOldWeekLogistica] = useState(false);
  const [timeRangeLogistica, setTimeRangeLogistica] = useState("4w");
  const [filteredKpisLogistica, setFilteredKpisLogistica] = useState<any[]>([]);
  const [showAnalyticsLogistica, setShowAnalyticsLogistica] = useState(false);

  const [loadingPdfDownload, setLoadingPdfDownload] = useState(false);

  // Estado para mostrar el formulario inline en lugar de los charts
  const [showInlineForm, setShowInlineForm] = useState(false);

  // Estados para el diálogo de gráficas
  const [chartDialog, setChartDialog] = useState({
    isOpen: false,
    title: '',
    chartData: null as any,
    chartOptions: null as any,
    chartType: 'line' as 'line' | 'pie' | 'doughnut' | 'bar'
  });

  // Efecto para detectar scroll y mostrar/ocultar shadow del navbar
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Ref para el canvas de la gráfica extendida
  const chartCanvasRef = useRef<any>(null);
  const [showKpiHistory, setShowKpiHistory] = useState(false); // Mostrar por defecto
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [isAddingOldWeek, setIsAddingOldWeek] = useState(false);
  const [selectedKpis, setSelectedKpis] = useState<string[]>([]);

  // Estados para el dialog de confirmación
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    loading: false
  });



  // Función para descargar gráfica individual como PNG (mejorada)
  const downloadChartAsPNG = (title: string) => {
    console.log(`Iniciando descarga de gráfico: ${title}`);

    try {
      // Buscar el canvas de Chart.js en el modal
      const chartContainer = document.getElementById('chart-dialog');
      if (!chartContainer) {
        console.warn('No se encontró el contenedor del gráfico');
        return;
      }

      const canvas = chartContainer.querySelector('canvas');
      if (!canvas) {
        console.warn('No se encontró el canvas del gráfico');
        return;
      }

      // Verificar si el gráfico está completamente renderizado
      const chartInstance = ChartJS.getChart(canvas);
      if (!chartInstance) {
        console.warn('No se encontró la instancia del gráfico');
        return;
      }

      // Función para realizar la descarga con múltiples métodos
      const performDownload = () => {
        const filename = `${ChartExportUtils.sanitizeFilename(title)}.png`;

        try {
          // Método 1: Usar toBase64Image() de Chart.js (recomendado)
          if (typeof chartInstance.toBase64Image === 'function') {
            const imageData = chartInstance.toBase64Image('image/png', 1.0);

            // Crear enlace de descarga
            const link = document.createElement('a');
            link.href = imageData;
            link.download = filename;

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log(`Gráfico "${title}" descargado exitosamente usando Chart.js toBase64Image`);
            return;
          }
        } catch (chartError) {
          console.warn('Error con toBase64Image de Chart.js, intentando método alternativo:', chartError);
        }

        try {
          // Método 2: Usar canvas.toBlob() (más moderno)
          ChartExportUtils.downloadImageBlob(canvas, filename);
          console.log(`Gráfico "${title}" descargado exitosamente usando canvas.toBlob`);
          return;
        } catch (blobError) {
          console.warn('Error con canvas.toBlob, intentando método fallback:', blobError);
        }

        try {
          // Método 3: Fallback con canvas.toDataURL()
          const fallbackImageData = canvas.toDataURL('image/png', 1.0);
          const link = document.createElement('a');
          link.href = fallbackImageData;
          link.download = filename;

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          console.log(`Gráfico "${title}" descargado con método fallback canvas.toDataURL`);
        } catch (fallbackError) {
          console.error('Todos los métodos de descarga fallaron:', fallbackError);
        }
      };

      // Verificar si hay animaciones activas y esperar si es necesario
      if (chartInstance.options?.animation !== false && chartInstance.options?.animation) {
        // Esperar un momento para que terminen las animaciones
        setTimeout(performDownload, 500);
      } else {
        // Sin animaciones, descargar inmediatamente
        performDownload();
      }

    } catch (error) {
      console.error('Error al descargar la gráfica:', error);
    }
  };

  // Función avanzada para exportar gráficos con opciones personalizadas
  const exportChartWithOptions = async (
    title: string,
    format: 'png' | 'jpg' | 'webp' = 'png',
    quality: number = 1.0,
    dimensions?: { width: number; height: number }
  ) => {
    console.log(`Exportando gráfico "${title}" en formato ${format.toUpperCase()} con calidad ${quality}`);

    try {
      const chartContainer = document.getElementById('chart-dialog');
      if (!chartContainer) {
        throw new Error('No se encontró el contenedor del gráfico');
      }

      const canvas = chartContainer.querySelector('canvas');
      if (!canvas) {
        throw new Error('No se encontró el canvas del gráfico');
      }

      const chartInstance = ChartJS.getChart(canvas);
      if (!chartInstance) {
        throw new Error('No se encontró la instancia del gráfico');
      }

      // Si se especifican dimensiones personalizadas, crear un nuevo canvas
      let exportCanvas = canvas;
      if (dimensions) {
        exportCanvas = document.createElement('canvas');
        exportCanvas.width = dimensions.width;
        exportCanvas.height = dimensions.height;

        const exportCtx = exportCanvas.getContext('2d');
        if (!exportCtx) {
          throw new Error('No se pudo crear contexto para canvas de exportación');
        }

        // Configurar fondo blanco
        exportCtx.fillStyle = '#ffffff';
        exportCtx.fillRect(0, 0, exportCanvas.width, exportCanvas.height);

        // Crear gráfico temporal con nuevas dimensiones
        const exportChart = new ChartJS(exportCtx, {
          type: (chartInstance.config as any).type,
          data: (chartInstance.config as any).data,
          options: ChartExportUtils.getExportConfig((chartInstance.config as any).options)
        });

        // Esperar renderizado
        await ChartExportUtils.waitForChartRender(exportChart);

        // Limpiar después de la exportación
        setTimeout(() => exportChart.destroy(), 100);
      } else {
        // Usar canvas existente
        await ChartExportUtils.waitForChartRender(chartInstance);
      }

      // Determinar tipo MIME
      const mimeType = `image/${format}`;
      const filename = `${ChartExportUtils.sanitizeFilename(title)}.${format}`;

      // Exportar usando el método más apropiado
      if (format === 'png' || format === 'jpg') {
        // Para PNG y JPG, usar toBlob para mejor calidad
        exportCanvas.toBlob((blob) => {
          if (!blob) {
            console.error('No se pudo crear blob de la imagen');
            return;
          }

          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = filename;

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          URL.revokeObjectURL(url);
          console.log(`Gráfico exportado exitosamente: ${filename}`);
        }, mimeType, quality);
      } else {
        // Para otros formatos, usar toDataURL
        const dataURL = exportCanvas.toDataURL(mimeType, quality);
        const link = document.createElement('a');
        link.href = dataURL;
        link.download = filename;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log(`Gráfico exportado exitosamente: ${filename}`);
      }

    } catch (error) {
      console.error(`Error al exportar gráfico en formato ${format}:`, error);
    }
  };

  // Función para obtener todas las gráficas disponibles
  const getAllChartsData = () => {
    const charts = [];

    // Gráficas de Ventas
    if (activeTab === 'ventas' && evolucionActual.length > 0) {
      // 1. Volumen Total de Venta
      charts.push({
        title: 'Volumen Total de Venta',
        type: 'line' as const,
        data: {
          labels: evolucionActual.map(item => item.mes),
          datasets: [{
            label: 'Volumen (Litros)',
            data: evolucionActual.map(item => Number(item.volumen.toFixed(0))),
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#3b82f6',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${(context.parsed.y / 1000).toFixed(1)}K Litros`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Volumen (Litros)' },
              ticks: { callback: (value: any) => `${(value / 1000).toFixed(0)}K` }
            }
          }
        }
      });

      // 2. Crecimiento de Ventas
      charts.push({
        title: 'Crecimiento de Ventas',
        type: 'line' as const,
        data: {
          labels: evolucionActual.map(item => item.mes),
          datasets: [{
            label: 'Crecimiento (%)',
            data: evolucionActual.map(item => Number(item.crecimiento.toFixed(2))),
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#10b981',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.parsed.y.toFixed(2)}% de crecimiento`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Crecimiento (%)' },
              ticks: { callback: (value: any) => `${Number(value).toFixed(2)}%` }
            }
          }
        }
      });

      // 3. Margen Bruto por Litro
      charts.push({
        title: 'Margen Bruto por Litro',
        type: 'line' as const,
        data: {
          labels: evolucionActual.map(item => item.mes),
          datasets: [{
            label: 'Margen Bruto',
            data: evolucionActual.map(item => Number(item.margen.toFixed(2))),
            borderColor: '#f59e0b',
            backgroundColor: 'rgba(245, 158, 11, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#f59e0b',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `$${context.parsed.y.toFixed(2)} por litro`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Margen ($)' },
              ticks: { callback: (value: any) => `$${value.toFixed(2)}` }
            }
          }
        }
      });

      // 4. Tasa de Retención de Clientes (Doughnut)
      charts.push({
        title: 'Distribución de la Tasa de Retención de Clientes',
        type: 'doughnut' as const,
        data: {
          labels: ['Retención Actual', 'Objetivo Restante'],
          datasets: [{
            data: [Number(indicadoresActuales.tasaRetencionClientes.toFixed(2)), Number((100 - indicadoresActuales.tasaRetencionClientes).toFixed(2))],
            backgroundColor: ['#3b82f6', '#ef4444'],
            borderWidth: 0,
            cutout: '70%'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          layout: {
            padding: 20 // Agregar padding para evitar cortes de etiquetas
          },
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${Number(context.parsed).toFixed(2)}% ${context.label}`
              }
            }
          }
        }
      });

      // 5. Evolución de la Tasa de Retención
      charts.push({
        title: 'Tasa de Retención de Clientes',
        type: 'line' as const,
        data: {
          labels: evolucionActual.map(item => item.mes),
          datasets: [{
            label: 'Tasa de Retención (%)',
            data: evolucionActual.map(item => Number(item.retencion.toFixed(2))),
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#10b981',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.parsed.y.toFixed(2)}% de retención`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Retención (%)' },
              ticks: { callback: (value: any) => `${Number(value).toFixed(2)}%` }
            }
          }
        }
      });

      // 6. Cumplimiento de Objetivos (Doughnut)
      charts.push({
        title: 'Distribución del Cumplimiento de Objetivos',
        type: 'doughnut' as const,
        data: {
          labels: ['Cumplimiento Actual', 'Objetivo Restante'],
          datasets: [{
            data: [Number(indicadoresActuales.cumplimientoObjetivo.toFixed(2)), Number((100 - indicadoresActuales.cumplimientoObjetivo).toFixed(2))],
            backgroundColor: ['#10b981', '#f59e0b'],
            borderWidth: 0,
            cutout: '70%'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          layout: {
            padding: 20 // Agregar padding para evitar cortes de etiquetas
          },
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${Number(context.parsed).toFixed(2)}% ${context.label}`
              }
            }
          }
        }
      });

      // 7. Desviación de Ventas
      charts.push({
        title: 'Desviación entre Ventas Proyectadas y Reales',
        type: 'line' as const,
        data: {
          labels: evolucionActual.map(item => item.mes),
          datasets: [{
            label: 'Desviación (%)',
            data: evolucionActual.map(item => Number(item.desviacion.toFixed(2))),
            borderColor: '#f97316',
            backgroundColor: 'rgba(249, 115, 22, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#f97316',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.parsed.y.toFixed(2)}% de desviación`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Desviación (%)' },
              ticks: { callback: (value: any) => `${Number(value).toFixed(2)}%` }
            }
          }
        }
      });

      // 8. Ciclo de Cierre
      charts.push({
        title: 'Ciclo Promedio de Cierre de Ventas',
        type: 'line' as const,
        data: {
          labels: evolucionActual.map(item => item.mes),
          datasets: [{
            label: 'Ciclo de Cierre (días)',
            data: evolucionActual.map(item => Number(item.ciclo.toFixed(0))),
            borderColor: '#14b8a6',
            backgroundColor: 'rgba(20, 184, 166, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#14b8a6',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.parsed.y.toFixed(0)} días promedio`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Días' },
              ticks: { callback: (value: any) => `${Number(value).toFixed(0)} días` }
            }
          }
        }
      });

      // 9. Clientes Activos
      charts.push({
        title: 'Número de Clientes Activos',
        type: 'line' as const,
        data: {
          labels: evolucionActual.map(item => item.mes),
          datasets: [{
            label: 'Clientes Activos',
            data: evolucionActual.map(item => Number(item.clientesActivos.toFixed(0))),
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#10b981',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${Number(context.parsed.y).toFixed(0).toLocaleString()} clientes`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Número de Clientes' },
              ticks: { callback: (value: any) => Number(value).toFixed(0).toLocaleString() }
            }
          }
        }
      });
    }

    // Gráficas de Compras
    if (activeTab === 'compras' && kpisCompras.length > 0) {
      // Procesar datos de compras similar a como se hace en AdminDashboardCompras
      const filteredKpisCompras = kpisCompras.slice(-4); // Últimas 4 semanas
      const evolutionDataCompras = filteredKpisCompras.reverse().map((kpi: any) => ({
        mes: formatWeekDatesForChart(kpi.year, kpi.weekNumber),
        proveedores: kpi.numeroProveedoresActivos,
        reporteGanancia: kpi.porcentajeReporteGanancia,
        precioPromedio: kpi.preciosPromedioCompra,
        diferencial: kpi.diferencialPrecioPemex,
        distribucionProveedores: kpi.distribucionProveedores
      }));

      // 1. Número de Proveedores Activos
      charts.push({
        title: 'Número de Proveedores Activos',
        type: 'line' as const,
        data: {
          labels: evolutionDataCompras.map(item => item.mes),
          datasets: [{
            label: 'Número de Proveedores Activos',
            data: evolutionDataCompras.map(item => item.proveedores),
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#3b82f6',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.parsed.y} proveedores activos`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Número de Proveedores' },
              ticks: { callback: (value: any) => value.toString() }
            }
          }
        }
      });

      // 2. Porcentaje de Reporte de Ganancia
      charts.push({
        title: 'Porcentaje de Reporte de Ganancia',
        type: 'line' as const,
        data: {
          labels: evolutionDataCompras.map(item => item.mes),
          datasets: [{
            label: 'Porcentaje de Reporte de Ganancia (%)',
            data: evolutionDataCompras.map(item => Number(item.reporteGanancia.toFixed(2))),
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#10b981',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.parsed.y.toFixed(2)}% de reportes`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Porcentaje (%)' },
              ticks: { callback: (value: any) => `${Number(value).toFixed(2)}%` }
            }
          }
        }
      });

      // 3. Precios Promedios de Compra
      charts.push({
        title: 'Precios Promedios de Compra',
        type: 'line' as const,
        data: {
          labels: evolutionDataCompras.map(item => item.mes),
          datasets: [{
            label: 'Precio Promedio de Compra ($)',
            data: evolutionDataCompras.map(item => item.precioPromedio),
            borderColor: '#f59e0b',
            backgroundColor: 'rgba(245, 158, 11, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#f59e0b',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `$${context.parsed.y.toFixed(2)} por litro`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Precio ($)' },
              ticks: { callback: (value: any) => `$${value.toFixed(2)}` }
            }
          }
        }
      });

      // 4. Diferencial de Precio PEMEX
      charts.push({
        title: 'Diferencial entre Precio de Compra Real y Precio de Terminal PEMEX',
        type: 'line' as const,
        data: {
          labels: evolutionDataCompras.map(item => item.mes),
          datasets: [{
            label: 'Diferencial de Precio (%)',
            data: evolutionDataCompras.map(item => Number(item.diferencial.toFixed(2))),
            borderColor: '#8b5cf6',
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#8b5cf6',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.parsed.y.toFixed(2)}% diferencial`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Diferencial (%)' },
              ticks: { callback: (value: any) => `${Number(value).toFixed(2)}%` }
            }
          }
        }
      });

      // 5. Distribución de Compras por Proveedor (Pie Chart)
      if (filteredKpisCompras.length > 0 && filteredKpisCompras[0].distribucionProveedores) {
        const distribucion = filteredKpisCompras[0].distribucionProveedores;
        const labels = Object.keys(distribucion);
        const data = Object.values(distribucion);

        charts.push({
          title: 'Distribución de Compras por Proveedor',
          type: 'pie' as const,
          data: {
            labels,
            datasets: [{
              data,
              backgroundColor: [
                '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6',
                '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
              ],
              borderWidth: 2,
              borderColor: '#ffffff'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: { display: true, position: 'bottom' as const },
              tooltip: {
                callbacks: {
                  label: (context: any) => `${context.label}: ${Number(context.parsed).toFixed(2)}%`
                }
              }
            }
          }
        });
      }
    }

    // Gráficas de Logística
    if (activeTab === 'logistica' && filteredKpisLogistica.length > 0) {
      // Procesar datos de logística usando el filtro temporal
      const evolutionDataLogistica = filteredKpisLogistica.slice().reverse().map((kpi: any) => ({
        mes: formatWeekDatesForChart(kpi.year, kpi.weekNumber),
        unidadesConfirmadas: kpi.unidadesConfirmadas,
        unidadesSolicitadas: kpi.unidadesSolicitadas,
        porcentajeEntregasTiempo: kpi.porcentajeEntregasTiempo,
        porcentajeRetardos: kpi.porcentajeRetardos,
        porcentajeReprogramaciones: kpi.porcentajeReprogramaciones,
        promedioKmOperacion: kpi.promedioKmOperacion,
        promedioCostoFleteLitro: kpi.promedioCostoFleteLitro,
        promedioCostoFleteOperacion: kpi.promedioCostoFleteOperacion,
        pagoSemanalFlete: kpi.pagoSemanalFlete,
        pagoSemanalPenalizaciones: kpi.pagoSemanalPenalizaciones,
        porcentajeRutasCotizadas: kpi.porcentajeRutasCotizadas,
        porcentajeTransportistas: kpi.porcentajeTransportistas
      }));

      // 1. Unidades Confirmadas vs Solicitadas (Gráfico de barras comparativo)
      charts.push({
        title: 'Unidades Confirmadas vs Solicitadas',
        type: 'bar' as const,
        data: {
          labels: evolutionDataLogistica.map(item => item.mes),
          datasets: [
            {
              label: 'Unidades Confirmadas',
              data: evolutionDataLogistica.map(item => item.unidadesConfirmadas),
              backgroundColor: 'rgba(16, 185, 129, 0.8)',
              borderColor: '#10b981',
              borderWidth: 1,
              stack: 'stack1'
            },
            {
              label: 'Unidades No Confirmadas',
              data: evolutionDataLogistica.map(item => Math.max(0, item.unidadesSolicitadas - item.unidadesConfirmadas)),
              backgroundColor: 'rgba(239, 68, 68, 0.6)',
              borderColor: '#ef4444',
              borderWidth: 1,
              stack: 'stack1'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.dataset.label}: ${context.parsed.y} unidades`,
                afterBody: (tooltipItems: any) => {
                  const dataIndex = tooltipItems[0].dataIndex;
                  const semanaData = evolutionDataLogistica[dataIndex];
                  return [
                    `Total Solicitadas: ${semanaData.unidadesSolicitadas} unidades`,
                    `Total Confirmadas: ${semanaData.unidadesConfirmadas} unidades`,
                    `Tasa de Confirmación: ${((semanaData.unidadesConfirmadas / semanaData.unidadesSolicitadas) * 100).toFixed(1)}%`
                  ];
                }
              }
            }
          },
          scales: {
            x: {
              display: true,
              stacked: true,
              title: { display: true, text: 'Período' }
            },
            y: {
              display: true,
              stacked: true,
              title: { display: true, text: 'Número de Unidades' },
              ticks: { callback: (value: any) => value.toString() }
            }
          }
        }
      });

      // 2. Porcentajes de Entregas, Retardos y Reprogramaciones (Líneas múltiples)
      charts.push({
        title: 'Entregas a Tiempo, Retardos y Reprogramaciones',
        type: 'line' as const,
        data: {
          labels: evolutionDataLogistica.map(item => item.mes),
          datasets: [
            {
              label: 'Entregas a Tiempo (%)',
              data: evolutionDataLogistica.map(item => Number(item.porcentajeEntregasTiempo.toFixed(2))),
              borderColor: '#10b981',
              backgroundColor: 'rgba(16, 185, 129, 0.1)',
              borderWidth: 3,
              fill: false,
              tension: 0.4,
              pointRadius: 4,
              pointHoverRadius: 6,
              pointBackgroundColor: '#10b981',
              pointBorderColor: '#ffffff',
              pointBorderWidth: 2
            },
            {
              label: 'Retardos / Estadías (%)',
              data: evolutionDataLogistica.map(item => Number(item.porcentajeRetardos.toFixed(2))),
              borderColor: '#f59e0b',
              backgroundColor: 'rgba(245, 158, 11, 0.1)',
              borderWidth: 3,
              fill: false,
              tension: 0.4,
              pointRadius: 4,
              pointHoverRadius: 6,
              pointBackgroundColor: '#f59e0b',
              pointBorderColor: '#ffffff',
              pointBorderWidth: 2
            },
            {
              label: 'Reprogramaciones (%)',
              data: evolutionDataLogistica.map(item => Number(item.porcentajeReprogramaciones.toFixed(2))),
              borderColor: '#ef4444',
              backgroundColor: 'rgba(239, 68, 68, 0.1)',
              borderWidth: 3,
              fill: false,
              tension: 0.4,
              pointRadius: 4,
              pointHoverRadius: 6,
              pointBackgroundColor: '#ef4444',
              pointBorderColor: '#ffffff',
              pointBorderWidth: 2
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.dataset.label}: ${context.parsed.y.toFixed(2)}%`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Porcentaje (%)' },
              ticks: { callback: (value: any) => `${Number(value).toFixed(2)}%` }
            }
          }
        }
      });

      // 3. Promedio de KM recorridos por operación
      charts.push({
        title: 'Promedio de KM Recorridos por Operación',
        type: 'bar' as const,
        data: {
          labels: evolutionDataLogistica.map(item => item.mes),
          datasets: [{
            label: 'KM por Operación',
            data: evolutionDataLogistica.map(item => Number(item.promedioKmOperacion.toFixed(2))),
            backgroundColor: 'rgba(139, 92, 246, 0.8)',
            borderColor: '#8b5cf6',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.parsed.y.toFixed(2)} KM`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Kilómetros' },
              ticks: { callback: (value: any) => `${Number(value).toFixed(0)} KM` }
            }
          }
        }
      });

      // 4. Promedio de Costo de Flete por Litro
      charts.push({
        title: 'Promedio de Costo de Flete por Litro',
        type: 'line' as const,
        data: {
          labels: evolutionDataLogistica.map(item => item.mes),
          datasets: [{
            label: 'Costo por Litro ($)',
            data: evolutionDataLogistica.map(item => Number(item.promedioCostoFleteLitro.toFixed(2))),
            borderColor: '#06b6d4',
            backgroundColor: 'rgba(6, 182, 212, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#06b6d4',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `$${context.parsed.y.toFixed(2)} por litro`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Costos en Pesos ($)' },
              ticks: { callback: (value: any) => `$${Number(value).toFixed(2)}` }
            }
          }
        }
      });

      // 5. Promedio de Costo de Flete por Operación
      charts.push({
        title: 'Promedio de Costo de Flete por Operación',
        type: 'line' as const,
        data: {
          labels: evolutionDataLogistica.map(item => item.mes),
          datasets: [
            {
              label: 'Costo por Operación ($)',
              data: evolutionDataLogistica.map(item => Number(item.promedioCostoFleteOperacion.toFixed(2))),
              borderColor: '#84cc16',
              backgroundColor: 'rgba(132, 204, 22, 0.1)',
              borderWidth: 3,
              fill: true,
              tension: 0.4,
              pointRadius: 4,
              pointHoverRadius: 6,
              pointBackgroundColor: '#84cc16',
              pointBorderColor: '#ffffff',
              pointBorderWidth: 2
            },
            {
              label: 'Promedio General',
              data: evolutionDataLogistica.map(() => {
                const promedio = evolutionDataLogistica.reduce((sum, item) => sum + item.promedioCostoFleteOperacion, 0) / evolutionDataLogistica.length;
                return Number(promedio.toFixed(2));
              }),
              borderColor: '#ef4444',
              borderWidth: 2,
              borderDash: [5, 5],
              fill: false,
              pointRadius: 0,
              pointHoverRadius: 0,
              tension: 0,
              datalabels: {
                display: function(context: any) {
                  return context.dataIndex === Math.floor(context.dataset.data.length / 2); // Solo mostrar en el punto medio
                },
                align: 'top',
                backgroundColor: '#ef4444',
                borderRadius: 8,
                color: 'white',
                font: { weight: 'bold' },
                padding: 6,
                formatter: (value: any) => `$${Number(value).toFixed(0)}`
              }
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `$${context.parsed.y.toFixed(2)} por operación`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Costos en Pesos ($)' },
              ticks: { callback: (value: any) => `$${Number(value).toFixed(2)}` }
            }
          }
        }
      });

      // 6. Pago Semanal de Flete
      charts.push({
        title: 'Pago Semanal de Flete',
        type: 'bar' as const,
        data: {
          labels: evolutionDataLogistica.map(item => item.mes),
          datasets: [{
            label: 'Pago Semanal ($)',
            data: evolutionDataLogistica.map(item => Number(item.pagoSemanalFlete.toFixed(2))),
            backgroundColor: 'rgba(99, 102, 241, 0.6)',
            borderColor: '#6366f1',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `$${context.parsed.y.toFixed(2)}`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Monto en Pesos ($)' },
              ticks: { callback: (value: any) => `$${Number(value).toFixed(0)}` }
            }
          }
        }
      });

      // 7. Pago Semanal por Penalizaciones
      charts.push({
        title: 'Pago Semanal por Penalizaciones',
        type: 'bar' as const,
        data: {
          labels: evolutionDataLogistica.map(item => item.mes),
          datasets: [{
            label: 'Penalizaciones ($)',
            data: evolutionDataLogistica.map(item => Number(item.pagoSemanalPenalizaciones.toFixed(2))),
            backgroundColor: 'rgba(239, 68, 68, 0.6)',
            borderColor: '#ef4444',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `$${context.parsed.y.toFixed(2)}`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Monto en Pesos ($)' },
              ticks: { callback: (value: any) => `$${Number(value).toFixed(0)}` }
            }
          }
        }
      });

      // 8. Porcentaje de Rutas Cotizadas a Tiempo
      charts.push({
        title: 'Porcentaje de Rutas Cotizadas a Tiempo',
        type: 'bar' as const,
        data: {
          labels: evolutionDataLogistica.map(item => item.mes),
          datasets: [{
            label: 'Rutas Cotizadas a Tiempo (%)',
            data: evolutionDataLogistica.map(item => Number(item.porcentajeRutasCotizadas.toFixed(2))),
            backgroundColor: 'rgba(249, 115, 22, 0.8)',
            borderColor: '#f97316',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.parsed.y.toFixed(2)}%`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Porcentaje (%)' },
              ticks: { callback: (value: any) => `${Number(value).toFixed(2)}%` }
            }
          }
        }
      });

      // 9. Porcentaje de Transportistas Clasificados
      charts.push({
        title: 'Porcentaje de Transportistas Clasificados',
        type: 'line' as const,
        data: {
          labels: evolutionDataLogistica.map(item => item.mes),
          datasets: [{
            label: 'Transportistas Clasificados (%)',
            data: evolutionDataLogistica.map(item => Number(item.porcentajeTransportistas.toFixed(2))),
            borderColor: '#14b8a6',
            backgroundColor: 'rgba(20, 184, 166, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#14b8a6',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.parsed.y.toFixed(2)}%`
              }
            }
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Porcentaje (%)' },
              ticks: { callback: (value: any) => `${Number(value).toFixed(2)}%` }
            }
          }
        }
      });
    }

    return charts;
  };

  // Función mejorada para generar PDF con descarga directa
  const downloadAllChartsAsPDF = async () => {
    setLoadingPdfDownload(true);
    try {
      const charts = getAllChartsData();
      if (charts.length === 0) {
        console.warn('No hay gráficos disponibles para exportar');
        setLoadingPdfDownload(false);
        return;
      }

      console.log(`Iniciando exportación PDF de ${charts.length} gráficos...`);

      // Configuración para PDF A4
      const PDF_CONFIG = {
        format: 'a4' as const,
        orientation: 'portrait' as const,
        unit: 'mm' as const,
        chartWidth: 160,  // Ancho en mm para A4
        chartHeight: 90,  // Alto en mm
        margin: 20,
        chartsPerPage: 2
      };

      // Crear documento PDF
      const pdf = new jsPDF({
        orientation: PDF_CONFIG.orientation,
        unit: PDF_CONFIG.unit,
        format: PDF_CONFIG.format
      });

      // Configuración de dimensiones para canvas
      const canvasWidth = 800;  // Píxeles para alta calidad
      const canvasHeight = 450;

      // Agregar header al PDF
      const currentDate = new Date().toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      pdf.setFontSize(20);
      pdf.setTextColor(249, 115, 22); // Color naranja
      pdf.text('Reporte de Gráficas', 105, 30, { align: 'center' });

      pdf.setFontSize(12);
      pdf.setTextColor(102, 102, 102); // Color gris
      pdf.text(`Cassiopeia Petrolíferos - ${currentDate}`, 105, 40, { align: 'center' });

      let currentY = 60; // Posición Y inicial
      let pageNumber = 1;

      // Procesar cada gráfico
      for (let i = 0; i < charts.length; i++) {
        const chart = charts[i];
        console.log(`Procesando gráfico ${i + 1}/${charts.length}: ${chart.title}`);

        try {
          // Crear canvas temporal para el gráfico
          const chartCanvas = document.createElement('canvas');
          chartCanvas.width = canvasWidth;
          chartCanvas.height = canvasHeight;

          const chartCtx = chartCanvas.getContext('2d');
          if (!chartCtx) {
            console.warn(`No se pudo crear contexto para gráfico: ${chart.title}`);
            continue;
          }

          // Configurar fondo blanco
          chartCtx.fillStyle = '#ffffff';
          chartCtx.fillRect(0, 0, chartCanvas.width, chartCanvas.height);

          // Configuración optimizada para exportación PDF
          const exportOptions = {
            ...chart.options,
            responsive: false,
            maintainAspectRatio: false,
            animation: false,
            devicePixelRatio: 2,
            plugins: {
              ...chart.options.plugins,
              legend: {
                display: true,
                position: 'bottom' as const,
                labels: {
                  padding: 15,
                  usePointStyle: true,
                  font: {
                    size: 12,
                    weight: 'bold' as const
                  },
                  color: '#374151'
                }
              },
              // Configurar datalabels para mostrar etiquetas en PDF sin cortes
              datalabels: {
                align: function(context: any) {
                  const chartType = context.chart.config.type;

                  // Para gráficos circulares, centrar las etiquetas dentro del segmento
                  if (chartType === 'pie' || chartType === 'doughnut') {
                    return 'center';
                  }

                  // Para gráficos lineales, usar lógica inteligente como en getSmartDataLabelsConfig
                  if (chartType === 'line') {
                    // Validar que tenemos todos los datos necesarios
                    if (!context || !context.chart || !context.parsed || typeof context.parsed.y !== 'number') {
                      return 'start';
                    }

                    const chart = context.chart;
                    const yScale = chart.scales?.y;
                    const xScale = chart.scales?.x;

                    // Validar que las escalas existen y tienen valores válidos
                    if (!yScale || typeof yScale.max !== 'number' || typeof yScale.min !== 'number') {
                      return 'start';
                    }

                    // Verificar posición horizontal
                    let isNearRightEdge = false;
                    if (xScale && typeof context.dataIndex === 'number' && context.dataset?.data) {
                      const dataLength = context.dataset.data.length;
                      const dataIndex = context.dataIndex;
                      // Considerar que está cerca del borde derecho si está en el último 30% de los datos
                      isNearRightEdge = dataIndex >= (dataLength * 0.7);
                    }

                    // Lógica de posicionamiento inteligente
                    if (isNearRightEdge) {
                      return 'end'; // Si está cerca del borde derecho, mostrar al final
                    } else {
                      return 'start'; // Posición por defecto al inicio
                    }
                  }

                  // Para otros tipos de gráfico
                  return 'end';
                },
                anchor: function(context: any) {
                  const chartType = context.chart.config.type;

                  // Para gráficos circulares, anclar al centro para evitar cortes
                  if (chartType === 'pie' || chartType === 'doughnut') {
                    return 'center';
                  }

                  // Para gráficos lineales, usar lógica inteligente
                  if (chartType === 'line') {
                    // Usar anchor dinámico para mejor posicionamiento
                    if (!context || !context.chart || !context.parsed || typeof context.parsed.y !== 'number') {
                      return 'start';
                    }

                    const xScale = context.chart.scales?.x;
                    if (xScale && typeof context.dataIndex === 'number' && context.dataset?.data) {
                      const dataLength = context.dataset.data.length;
                      const dataIndex = context.dataIndex;
                      const isNearRightEdge = dataIndex >= (dataLength * 0.7);

                      if (isNearRightEdge) {
                        return 'end'; // Para etiquetas al final
                      }
                    }

                    return 'start'; // Anchor por defecto al inicio
                  }

                  // Para otros tipos de gráfico
                  return 'end';
                },
                backgroundColor: function(context: any) {
                  const dataset = context.dataset;
                  const chartType = context.chart.config.type;

                  // Para gráficos circulares (pie/doughnut), usar backgroundColor del dataset
                  if (chartType === 'pie' || chartType === 'doughnut') {
                    if (dataset.backgroundColor) {
                      if (Array.isArray(dataset.backgroundColor)) {
                        return dataset.backgroundColor[context.dataIndex];
                      }
                      return dataset.backgroundColor;
                    }
                  }

                  // Para gráficos lineales, usar borderColor (como en getSmartDataLabelsConfig)
                  if (chartType === 'line') {
                    const datasetColor = dataset.borderColor || dataset.backgroundColor;
                    return datasetColor;
                  }

                  // Para otros tipos, usar backgroundColor
                  if (dataset.backgroundColor) {
                    if (Array.isArray(dataset.backgroundColor)) {
                      return dataset.backgroundColor[0];
                    }
                    return dataset.backgroundColor;
                  }

                  // Fallback a borderColor
                  if (dataset.borderColor) {
                    if (Array.isArray(dataset.borderColor)) {
                      return dataset.borderColor[0];
                    }
                    return dataset.borderColor;
                  }

                  // Color por defecto
                  return '#374151';
                },
                borderColor: function(context: any) {
                  const chartType = context.chart.config.type;
                  // Para gráficos lineales, usar borde semi-transparente como en los extendidos
                  if (chartType === 'line') {
                    return 'rgba(255, 255, 255, 0.8)';
                  }
                  // Para gráficos circulares, usar borde blanco sólido
                  return 'white';
                },
                borderRadius: function(context: any) {
                  const chartType = context.chart.config.type;
                  // Para gráficos circulares, usar bordes más redondeados
                  if (chartType === 'pie' || chartType === 'doughnut') {
                    return 25;
                  }
                  // Para gráficos lineales, usar bordes menos redondeados
                  return 8;
                },
                borderWidth: function(context: any) {
                  const chartType = context.chart.config.type;
                  // Para gráficos circulares, usar borde más grueso
                  if (chartType === 'pie' || chartType === 'doughnut') {
                    return 2;
                  }
                  // Para gráficos lineales, usar borde más delgado
                  return 1;
                },
                color: '#ffffff',
                font: {
                  size: 11,
                  weight: 'bold' as const,
                  family: 'Arial, sans-serif'
                },
                padding: function(context: any) {
                  const chartType = context.chart.config.type;
                  // Para gráficos circulares, usar padding más grande
                  if (chartType === 'pie' || chartType === 'doughnut') {
                    return 6;
                  }
                  // Para gráficos lineales, usar padding más pequeño
                  return 4;
                },
                formatter: function(value: any, context: any) {
                  try {
                    const chartType = context.chart.config.type;

                    // Asegurar que tenemos un valor numérico válido
                    const numValue = typeof value === 'number' ? value : parseFloat(value);
                    if (isNaN(numValue)) {
                      return value; // Retornar valor original si no es numérico
                    }

                    if (chartType === 'pie' || chartType === 'doughnut') {
                      // Para gráficos circulares, mostrar porcentaje
                      const total = context.dataset.data.reduce((sum: number, val: number) => sum + (val || 0), 0);
                      if (total === 0) return '0%';
                      const percentage = ((numValue / total) * 100).toFixed(2);
                      return `${percentage}%`;
                    } else if (chartType === 'line') {
                      // Para gráficos lineales, formatear números
                      if (numValue >= 1000000) {
                        return `${(numValue / 1000000).toFixed(1)}M`;
                      } else if (numValue >= 1000) {
                        return `${(numValue / 1000).toFixed(1)}K`;
                      } else if (numValue % 1 !== 0) {
                        return numValue.toFixed(2);
                      }
                      return numValue.toString();
                    }

                    // Para otros tipos de gráfico, formatear números grandes
                    if (numValue >= 1000000) {
                      return `${(numValue / 1000000).toFixed(1)}M`;
                    } else if (numValue >= 1000) {
                      return `${(numValue / 1000).toFixed(1)}K`;
                    }

                    return numValue.toString();
                  } catch (error) {
                    console.warn('Error en formatter de datalabels PDF:', error);
                    return value;
                  }
                },
                display: function(context: any) {
                  try {
                    const chartType = context.chart.config.type;

                    // Para gráficos circulares en PDF, mostrar siempre las etiquetas
                    if (chartType === 'pie' || chartType === 'doughnut') {
                      return true;
                    }

                    // Para gráficos lineales, mostrar siempre (como en getSmartDataLabelsConfig)
                    if (chartType === 'line') {
                      return true;
                    }

                    // Para otros tipos, validar que hay valor
                    let value;
                    value = context.parsed?.y ?? context.parsed ?? context.raw ?? context.dataset.data[context.dataIndex];

                    return value !== undefined && value !== null;
                  } catch (error) {
                    console.warn('Error en display function de datalabels PDF:', error);
                    return false;
                  }
                }
              }
            },
            scales: chart.options.scales ? {
              ...chart.options.scales,
              x: chart.options.scales.x ? {
                ...chart.options.scales.x,
                ticks: {
                  ...(chart.options.scales.x as any).ticks,
                  font: { size: 12 }
                }
              } : undefined,
              y: chart.options.scales.y ? {
                ...chart.options.scales.y,
                ticks: {
                  ...(chart.options.scales.y as any).ticks,
                  font: { size: 12 }
                }
              } : undefined
            } : undefined
          };

          // Crear instancia temporal de Chart.js
          const tempChart = new ChartJS(chartCtx, {
            type: chart.type,
            data: {
              ...chart.data,
              datasets: chart.data.datasets.map((dataset: any) => ({
                ...dataset,
                backgroundColor: dataset.backgroundColor,
                borderColor: dataset.borderColor,
                pointBackgroundColor: dataset.pointBackgroundColor,
                pointBorderColor: dataset.pointBorderColor,
                pointBorderWidth: dataset.pointBorderWidth,
                borderWidth: dataset.borderWidth
              }))
            },
            options: exportOptions
          });

          // Esperar renderizado
          await new Promise(resolve => setTimeout(resolve, 300));

          // Obtener imagen del gráfico
          const imageData = tempChart.toBase64Image('image/png', 1.0);

          // Verificar si necesitamos una nueva página
          if (i > 0 && i % PDF_CONFIG.chartsPerPage === 0) {
            pdf.addPage();
            currentY = 60;
            pageNumber++;
          }

          // Agregar título del gráfico
          pdf.setFontSize(14);
          pdf.setTextColor(31, 41, 55); // Color gris oscuro
          pdf.text(chart.title, 105, currentY, { align: 'center' });

          // Agregar imagen del gráfico al PDF
          pdf.addImage(
            imageData,
            'PNG',
            PDF_CONFIG.margin,
            currentY + 10,
            PDF_CONFIG.chartWidth,
            PDF_CONFIG.chartHeight
          );

          // Actualizar posición Y para el siguiente gráfico
          currentY += PDF_CONFIG.chartHeight + 30;

          // Limpiar instancia temporal
          tempChart.destroy();

          console.log(`Gráfico procesado y agregado al PDF: ${chart.title}`);
        } catch (error) {
          console.error(`Error procesando gráfico ${chart.title}:`, error);
        }
      }

      // Agregar footer al PDF
      pdf.setFontSize(10);
      pdf.setTextColor(156, 163, 175); // Color gris claro
      pdf.text(`Página ${pageNumber} - Generado el ${currentDate}`, 105, 280, { align: 'center' });

      // Descargar el PDF directamente
      const fileName = `Reporte_Graficas_${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(fileName);

      console.log(`PDF descargado exitosamente: ${fileName}`);

    } catch (error) {
      console.error('Error al generar PDF:', error);
    } finally {
      setLoadingPdfDownload(false);
    }
  };

  // Función para abrir el diálogo de gráficas
  const openChartDialog = (title: string, chartData: any, chartOptions: any, chartType: 'line' | 'pie' | 'doughnut' | 'bar') => {
    setChartDialog({
      isOpen: true,
      title,
      chartData,
      chartOptions: {
        ...chartOptions,
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          ...chartOptions.plugins,
          legend: {
            display: true,
            position: 'bottom' as const,
            labels: {
              padding: 20,
              usePointStyle: true,
              font: {
                size: 12
              }
            }
          }
        }
      },
      chartType
    });
  };







  // Funciones para manejar KPIs semanales
  const loadKpisSemanales = async () => {
    setLoadingKpis(true);
    try {
      const result = await getKpisSemanales({ limit: 10 });
      if (result.success && result.data) {
        const kpis = result.data;
        setKpisSemanales(kpis);

        // No mostrar automáticamente el historial al cargar

        // Actualizar estados con datos reales si existen
        if (kpis.length > 0) {
          const latestKpi = kpis[0];

          // Actualizar indicadores principales con datos reales
          const nuevosIndicadores = {
            volumenTotalLitros: latestKpi.volumenTotalLitros,
            crecimientoMensual: latestKpi.crecimientoMensual,
            margenBrutoPorLitro: latestKpi.margenBrutoPorLitro,
            tasaRetencionClientes: latestKpi.tasaRetencionClientes,
            cumplimientoObjetivo: latestKpi.cumplimientoObjetivo,
            desviacionVentas: latestKpi.desviacionVentas,
            cicloPromedioCierre: latestKpi.cicloPromedioCierre,
            clientesActivosMensuales: latestKpi.clientesActivosMensuales
          };
          setIndicadoresActuales(nuevosIndicadores);

          // Actualizar evolución de indicadores con datos reales (últimas 6 semanas)
          const evolutionData = kpis.slice(0, 6).reverse().map((kpi: any) => ({
            mes: formatWeekDatesForChart(kpi.year, kpi.weekNumber),
            volumen: kpi.volumenTotalLitros,
            crecimiento: kpi.crecimientoMensual,
            margen: kpi.margenBrutoPorLitro,
            retencion: kpi.tasaRetencionClientes,
            cumplimiento: kpi.cumplimientoObjetivo,
            desviacion: kpi.desviacionVentas,
            ciclo: kpi.cicloPromedioCierre,
            clientesActivos: kpi.clientesActivosMensuales
          }));

          // Si tenemos menos de 6 semanas de datos, completar con datos mock
          while (evolutionData.length < 6) {
            const lastIndex = evolutionData.length;
            evolutionData.unshift({
              mes: `S${27 - lastIndex}`,
              volumen: 2200000 + (lastIndex * 50000),
              crecimiento: 5.2 + (lastIndex * 0.5),
              margen: 2.15 + (lastIndex * 0.05),
              retencion: 85.5 + (lastIndex * 0.3),
              cumplimiento: 92.3 + (lastIndex * 0.4),
              desviacion: -3.1 - (lastIndex * 0.2),
              ciclo: 14 - lastIndex,
              clientesActivos: 1650 + (lastIndex * 30)
            });
          }

          setEvolucionActual(evolutionData);

          // Actualizar distribuciones con datos reales
          const nuevaDistribucionCumplimiento = [
            { name: "Cumplido", value: latestKpi.cumplimientoObjetivo, color: "#10b981" },
            { name: "Pendiente", value: Math.max(0, 100 - latestKpi.cumplimientoObjetivo), color: "#f59e0b" }
          ];
          setDistribucionCumplimientoActual(nuevaDistribucionCumplimiento);

          const nuevaDistribucionRetencion = [
            { name: "Clientes Retenidos", value: latestKpi.tasaRetencionClientes, color: "#3b82f6" },
            { name: "Clientes Perdidos", value: Math.max(0, 100 - latestKpi.tasaRetencionClientes), color: "#ef4444" }
          ];
          setDistribucionRetencionActual(nuevaDistribucionRetencion);

          setHasRealData(true);
        } else {
          setHasRealData(false);
        }
      }
    } catch (error) {
      console.error('Error al cargar KPIs semanales:', error);
    } finally {
      setLoadingKpis(false);
    }
  };

  const handleSaveKpi = async (kpiData: KpiSemanalData) => {
    try {
      let result;

      if (editingKpi && editingKpi.id) {
        // Actualizar KPI existente
        result = await updateKpiSemanal(editingKpi.id, kpiData);
      } else {
        // Crear nuevo KPI
        result = await createKpiSemanal(kpiData);
      }

      if (result.success) {
        await loadKpisSemanales(); // Recargar datos
        setIsKpiModalOpen(false);
        setShowInlineForm(false); // Cerrar formulario inline
        setEditingKpi(null); // Limpiar estado de edición
      } else {
        alert(result.error || 'Error al guardar los datos');
      }
    } catch (error) {
      console.error('Error al guardar KPI:', error);
      alert('Error al guardar los datos');
    }
  };

  // Función para editar un KPI existente
  const handleEditKpi = (kpi: any) => {
    const kpiData: KpiSemanalData = {
      id: kpi.id,
      year: kpi.year,
      weekNumber: kpi.weekNumber,
      weekStartDate: kpi.weekStartDate.toISOString ? kpi.weekStartDate.toISOString() : kpi.weekStartDate,
      weekEndDate: kpi.weekEndDate.toISOString ? kpi.weekEndDate.toISOString() : kpi.weekEndDate,
      volumenTotalLitros: kpi.volumenTotalLitros,
      crecimientoMensual: kpi.crecimientoMensual,
      margenBrutoPorLitro: kpi.margenBrutoPorLitro,
      tasaRetencionClientes: kpi.tasaRetencionClientes,
      cumplimientoObjetivo: kpi.cumplimientoObjetivo,
      desviacionVentas: kpi.desviacionVentas,
      cicloPromedioCierre: kpi.cicloPromedioCierre,
      clientesActivosMensuales: kpi.clientesActivosMensuales
    };

    setEditingKpi(kpiData);
    setShowInlineForm(true);
  };

  // Función para eliminar un KPI
  const handleDeleteKpi = async (kpiId: string) => {
    // Buscar información del KPI para mostrar en el dialog
    const kpi = kpisSemanales.find(k => k.id === kpiId);
    const kpiInfo = kpi ? `Semana ${kpi.weekNumber}/${kpi.year}` : 'este KPI';

    setConfirmDialog({
      isOpen: true,
      title: 'Eliminar KPI',
      message: `¿Estás seguro de que quieres eliminar el KPI de ${kpiInfo}? Esta acción no se puede deshacer.`,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }));

        try {
          const result = await deleteKpiSemanal(kpiId);

          if (result.success) {
            await loadKpisSemanales(); // Recargar datos
            setConfirmDialog(prev => ({ ...prev, isOpen: false, loading: false }));
          } else {
            alert(result.error || 'Error al eliminar el KPI');
            setConfirmDialog(prev => ({ ...prev, loading: false }));
          }
        } catch (error) {
          console.error('Error al eliminar KPI:', error);
          alert('Error al eliminar el KPI');
          setConfirmDialog(prev => ({ ...prev, loading: false }));
        }
      },
      loading: false
    });
  };

  // Función para mostrar formulario inline de nuevo KPI
  const handleNewKpi = () => {
    setEditingKpi(null);
    setIsAddingOldWeek(false);
    setShowInlineForm(true);
  };



  // Función para agregar semana antigua
  const handleAddOldWeek = () => {
    setEditingKpi(null);
    setIsAddingOldWeek(true);
    setShowInlineForm(true);
  };

  // Función para eliminar KPIs seleccionados
  const handleDeleteSelected = async () => {
    if (selectedKpis.length === 0) return;

    const confirmMessage = `¿Estás seguro de que quieres eliminar ${selectedKpis.length} KPI${selectedKpis.length > 1 ? 's' : ''}? Esta acción no se puede deshacer.`;

    setConfirmDialog({
      isOpen: true,
      title: 'Eliminar KPIs Seleccionados',
      message: confirmMessage,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }));

        try {
          // Eliminar cada KPI seleccionado
          for (const kpiId of selectedKpis) {
            const result = await deleteKpiSemanal(kpiId);
            if (!result.success) {
              throw new Error(result.error || 'Error al eliminar KPI');
            }
          }

          // Limpiar selección después de eliminar
          setSelectedKpis([]);

          // Refrescar datos
          await loadKpisSemanales();

          setConfirmDialog(prev => ({ ...prev, isOpen: false, loading: false }));
        } catch (error) {
          console.error('Error al eliminar KPIs seleccionados:', error);
          alert('Error al eliminar algunos KPIs. Por favor, intenta de nuevo.');
          setConfirmDialog(prev => ({ ...prev, loading: false }));
        }
      },
      loading: false
    });
  };

  // Función para cargar KPIs de compras
  const loadKpisCompras = async () => {
    try {
      setLoadingKpisCompras(true);
      const result = await getKpisCompras({ limit: 50 });
      if (result.success && result.data) {
        setKpisCompras(result.data);
      }
    } catch (error) {
      console.error("Error al cargar KPIs de compras:", error);
    } finally {
      setLoadingKpisCompras(false);
    }
  };

  // Función para cargar KPIs de logística
  const loadKpisLogistica = async () => {
    try {
      setLoadingKpisLogistica(true);
      const result = await getKpisLogistica({ limit: 50 });
      if (result.success && result.data) {
        setKpisLogistica(result.data);
        // Inicializar con las primeras 4 semanas por defecto
        setFilteredKpisLogistica(result.data.slice(0, 4));
      }
    } catch (error) {
      console.error("Error al cargar KPIs de logística:", error);
    } finally {
      setLoadingKpisLogistica(false);
    }
  };

  // Funciones de manejo para KPIs de logística
  const handleSaveKpiLogistica = async (kpiData: KpiLogisticaData) => {
    try {
      let result;

      if (editingKpiLogistica && editingKpiLogistica.id) {
        // Actualizar KPI existente
        result = await updateKpiLogistica(editingKpiLogistica.id, kpiData);
      } else {
        // Crear nuevo KPI
        result = await createKpiLogistica(kpiData);
      }

      if (result.success) {
        await loadKpisLogistica(); // Recargar datos
        setShowKpiLogisticaModal(false);
        setShowInlineFormLogistica(false);
        setEditingKpiLogistica(null); // Limpiar estado de edición
      } else {
        alert(result.error || 'Error al guardar los datos');
      }
    } catch (error) {
      console.error('Error al guardar KPI de logística:', error);
      alert('Error al guardar los datos');
    }
  };

  const handleNewKpiLogistica = () => {
    setEditingKpiLogistica(null);
    setIsAddingOldWeekLogistica(false);
    setShowInlineFormLogistica(true);
  };

  const handleAddOldWeekLogistica = () => {
    setEditingKpiLogistica(null);
    setIsAddingOldWeekLogistica(true);
    setShowInlineFormLogistica(true);
  };

  useEffect(() => {
    // Cargar datos reales desde la API
    const loadData = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/admin/stats');
        if (response.ok) {
          const data = await response.json();
          setDashboardData({
            precios: data.tendenciaPrecios || [],
            terminales: data.ventasPorTerminal || [],
            leads: data.leadsConversion || [],
            tarifas: []
          });
          // Los datos de estadísticas generales se almacenan en dashboardData
          // Los KPIs semanales se manejan por separado en loadKpisSemanales()
        } else {
          console.error('Error al cargar estadísticas:', response.statusText);
        }
      } catch (error) {
        console.error('Error al cargar datos:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
    loadKpisSemanales();
    loadKpisCompras(); // Cargar también los KPIs de compras
    loadKpisLogistica(); // Cargar también los KPIs de logística
  }, []);

  // Efecto para filtrar datos según el rango de tiempo seleccionado
  useEffect(() => {
    if (!loading && kpisSemanales.length > 0) {
      const now = new Date();
      let weeksToShow = 4;

      // Determinar cuántas semanas mostrar según el filtro
      if (timeRange === "4w") {
        weeksToShow = 4;
      } else if (timeRange === "12w") {
        weeksToShow = 12;
      } else if (timeRange === "24w") {
        weeksToShow = 24;
      }

      // Filtrar KPIs por el período seleccionado
      const filteredKpis = kpisSemanales.slice(0, weeksToShow);

      if (filteredKpis.length > 0) {
        // Actualizar indicadores - volumen total suma acumulativa, otros indicadores promedio del período
        const avgIndicadores = {
          volumenTotalLitros: Math.round(filteredKpis.reduce((sum, kpi) => sum + kpi.volumenTotalLitros, 0)), // Suma total del período seleccionado
          crecimientoMensual: Number((filteredKpis.reduce((sum, kpi) => sum + kpi.crecimientoMensual, 0) / filteredKpis.length).toFixed(2)),
          margenBrutoPorLitro: Number((filteredKpis.reduce((sum, kpi) => sum + kpi.margenBrutoPorLitro, 0) / filteredKpis.length).toFixed(2)),
          tasaRetencionClientes: Number((filteredKpis.reduce((sum, kpi) => sum + kpi.tasaRetencionClientes, 0) / filteredKpis.length).toFixed(2)),
          cumplimientoObjetivo: Number((filteredKpis.reduce((sum, kpi) => sum + kpi.cumplimientoObjetivo, 0) / filteredKpis.length).toFixed(2)),
          desviacionVentas: Number((filteredKpis.reduce((sum, kpi) => sum + kpi.desviacionVentas, 0) / filteredKpis.length).toFixed(2)),
          cicloPromedioCierre: Math.round(filteredKpis.reduce((sum, kpi) => sum + kpi.cicloPromedioCierre, 0) / filteredKpis.length),
          clientesActivosMensuales: Math.round(filteredKpis.reduce((sum, kpi) => sum + kpi.clientesActivosMensuales, 0) / filteredKpis.length)
        };
        setIndicadoresActuales(avgIndicadores);

        // Actualizar evolución con los datos filtrados
        const evolutionData = filteredKpis.reverse().map((kpi: any) => ({
          mes: formatWeekDatesForChart(kpi.year, kpi.weekNumber),
          volumen: kpi.volumenTotalLitros,
          crecimiento: kpi.crecimientoMensual,
          margen: kpi.margenBrutoPorLitro,
          retencion: kpi.tasaRetencionClientes,
          cumplimiento: kpi.cumplimientoObjetivo,
          desviacion: kpi.desviacionVentas,
          ciclo: kpi.cicloPromedioCierre,
          clientesActivos: kpi.clientesActivosMensuales
        }));
        setEvolucionActual(evolutionData);

        // Actualizar distribuciones basadas en los datos filtrados
        const excelentWeeks = filteredKpis.filter(kpi => kpi.cumplimientoObjetivo >= 95).length;
        const goodWeeks = filteredKpis.filter(kpi => kpi.cumplimientoObjetivo >= 80 && kpi.cumplimientoObjetivo < 95).length;
        const poorWeeks = filteredKpis.filter(kpi => kpi.cumplimientoObjetivo < 80).length;

        setDistribucionCumplimientoActual([
          { name: "Excelente (≥95%)", value: excelentWeeks, color: "#16a34a" },
          { name: "Bueno (80-94%)", value: goodWeeks, color: "#f97316" },
          { name: "Necesita Mejora (<80%)", value: poorWeeks, color: "#dc2626" }
        ]);

        const highRetention = filteredKpis.filter(kpi => kpi.tasaRetencionClientes >= 90).length;
        const mediumRetention = filteredKpis.filter(kpi => kpi.tasaRetencionClientes >= 75 && kpi.tasaRetencionClientes < 90).length;
        const lowRetention = filteredKpis.filter(kpi => kpi.tasaRetencionClientes < 75).length;

        setDistribucionRetencionActual([
          { name: "Alta (≥90%)", value: highRetention, color: "#16a34a" },
          { name: "Media (75-89%)", value: mediumRetention, color: "#f97316" },
          { name: "Baja (<75%)", value: lowRetention, color: "#dc2626" }
        ]);
      }
    }
  }, [timeRange, loading, kpisSemanales]);
  // Efecto para filtrar datos de logística según el rango de tiempo seleccionado
  useEffect(() => {
    if (kpisLogistica.length > 0) {
      let weeksToShow = 4;

      // Determinar cuántas semanas mostrar según el filtro
      if (timeRangeLogistica === "4w") {
        weeksToShow = 4;
      } else if (timeRangeLogistica === "12w") {
        weeksToShow = 12;
      } else if (timeRangeLogistica === "24w") {
        weeksToShow = 24;
      }

      // Filtrar KPIs de logística por el período seleccionado
      const filteredKpis = kpisLogistica.slice(0, weeksToShow);
      setFilteredKpisLogistica(filteredKpis);
    }
  }, [kpisLogistica, timeRangeLogistica]);





  // Componente para el icono de información con tooltip
  const CalculationButton = memo(({ kpiId, calculation }: { kpiId: string; calculation: string }) => {
    const [isVisible, setIsVisible] = useState(false);

    const handleMouseEnter = useCallback(() => {
      setIsVisible(true);
    }, []);

    const handleMouseLeave = useCallback(() => {
      setIsVisible(false);
    }, []);

    return (
      <div className="relative inline-block">
        <div
          className="ml-2 p-1 cursor-default"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <Info className="h-3 w-3 text-gray-400" />
        </div>
        {isVisible && (
          <div className="absolute z-50 left-0 top-6 w-60 p-3 bg-white border border-gray-200 rounded-lg shadow-lg">
            <div className="font-medium text-gray-600 text-xs mb-1 flex items-center">
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-1.5"></div>
              Información
            </div>
            <div className="text-xs text-gray-500 leading-relaxed">
              {calculation}
            </div>
            <div className="absolute -top-1 left-2 w-2 h-2 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
          </div>
        )}
      </div>
    );
  });

  const StatCard = ({ title, value, icon: Icon, color, change }: any) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change && (
            <p className={`text-sm ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change > 0 ? '+' : ''}{change}% vs mes anterior
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
      </div>
    </div>
  );

  const tabs = [
    { id: "ventas", label: "Ventas", icon: ShoppingCart },
    { id: "compras", label: "Compras", icon: Package },
    { id: "logistica", label: "Logística", icon: Truck },
    { id: "contabilidad", label: "Contabilidad", icon: Calculator },
    { id: "operaciones", label: "Operaciones", icon: Settings },
    { id: "legal", label: "Legal", icon: Scale },
    { id: "planeacion", label: "Planeación", icon: Target },
    { id: "finanzas", label: "Finanzas", icon: Banknote },
  ];

  if (loading) {
    return (
      <div className="fixed top-0 left-0 w-full h-full bg-white flex items-center justify-center z-50">
        <Image
          alt="Logo Cassiopeia Petrolíferos | Combustibles Cassiopeia"
          width={355}
          src={appLogo}
          style={{ height: '10%', width: 'auto' }}
          priority
          loading="eager"
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-t from-orange-50/80 overflow-x-hidden">
      {/* Admin Header */}
      <header className={`link-color fixed top-0 inset-x-0 flex items-center z-50 w-full lg:bg-transparent transition-all py-5 bg-white lg:bg-white ${isScrolled ? 'shadow' : ''}`}>
        <div className="container">
          <nav className="flex items-center">
            <a href="/">
              <Image
                alt="Logo"
                loading="lazy"
                width={224}
                height={75}
                decoding="async"
                className=""
                src={appLogo}
                style={{ color: 'transparent' }}
              />
            </a>
            <div className="hidden lg:block mx-auto grow">
              <ul id="navbar-navlist" className="grow flex flex-col lg:flex-row lg:items-center lg:justify-center mt-4 lg:mt-0">
                <li className="nav-item pe-4">
                  <a className="nav-link flex items-center font-medium py-2 px-4 lg:py-0 text-primary" href="/admin">
                    <span className="shrink-0 me-2">
                      <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
                        <title>Stockholm-icons / Layout / Layout-4-blocks</title>
                        <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                          <rect id="bound" x="0" y="0" width="24" height="24"></rect>
                          <rect id="Rectangle-7" fill="currentColor" x="4" y="4" width="7" height="7" rx="1.5"></rect>
                          <path d="M5.5,13 L9.5,13 C10.3284271,13 11,13.6715729 11,14.5 L11,18.5 C11,19.3284271 10.3284271,20 9.5,20 L5.5,20 C4.67157288,20 4,19.3284271 4,18.5 L4,14.5 C4,13.6715729 4.67157288,13 5.5,13 Z M14.5,4 L18.5,4 C19.3284271,4 20,4.67157288 20,5.5 L20,9.5 C20,10.3284271 19.3284271,11 18.5,11 L14.5,11 C13.6715729,11 13,10.3284271 13,9.5 L13,5.5 C13,4.67157288 13.6715729,4 14.5,4 Z M14.5,13 L18.5,13 C19.3284271,13 20,13.6715729 20,14.5 L20,18.5 C20,19.3284271 19.3284271,20 18.5,20 L14.5,20 C13.6715729,20 13,19.3284271 13,18.5 L13,14.5 C13,13.6715729 13.6715729,13 14.5,13 Z" id="Combined-Shape" fill="currentColor" opacity="0.3"></path>
                        </g>
                      </svg>
                    </span>
                    <span className="grow">Dashboard</span>
                  </a>
                </li>
                <li className="nav-item pe-4">
                  <a className="nav-link flex items-center font-medium py-2 px-4 lg:py-0 text-gray-700 hover:text-primary transition-all" href="/tarifas">
                    <span className="shrink-0 me-2">
                      <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
                        <title>Stockholm-icons / Shopping / Dollar</title>
                        <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                          <rect x="0" y="0" width="24" height="24"></rect>
                          <path d="M6,2 L18,2 C18.5522847,2 19,2.44771525 19,3 C19,3.55228475 18.5522847,4 18,4 L6,4 C5.44771525,4 5,3.55228475 5,3 C5,2.44771525 5.44771525,2 6,2 Z" fill="currentColor" opacity="0.3"></path>
                          <path d="M7.5,5 L16.5,5 C17.3284271,5 18,5.67157288 18,6.5 C18,7.32842712 17.3284271,8 16.5,8 L7.5,8 C6.67157288,8 6,7.32842712 6,6.5 C6,5.67157288 6.67157288,5 7.5,5 Z" fill="currentColor" opacity="0.3"></path>
                          <path d="M5,9 L19,9 C19.5522847,9 20,9.44771525 20,10 L20,20 C20,21.1045695 19.1045695,22 18,22 L6,22 C4.8954305,22 4,21.1045695 4,20 L4,10 C4,9.44771525 4.44771525,9 5,9 Z M12,12 C10.8954305,12 10,12.8954305 10,14 C10,15.1045695 10.8954305,16 12,16 C13.1045695,16 14,15.1045695 14,14 C14,12.8954305 13.1045695,12 12,12 Z" fill="currentColor"></path>
                        </g>
                      </svg>
                    </span>
                    <span className="grow">Tarifas</span>
                  </a>
                </li>
                <li className="nav-item pe-4">
                  <a className="nav-link flex items-center font-medium py-2 px-4 lg:py-0 text-gray-700 hover:text-primary transition-all" href="/admin/settings">
                    <span className="shrink-0 me-2">
                      <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
                        <title>Stockholm-icons / Shopping / Settings</title>
                        <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                          <rect opacity="0.200000003" x="0" y="0" width="24" height="24"></rect>
                          <path d="M4.5,7 L9.5,7 C10.3284271,7 11,7.67157288 11,8.5 C11,9.32842712 10.3284271,10 9.5,10 L4.5,10 C3.67157288,10 3,9.32842712 3,8.5 C3,7.67157288 3.67157288,7 4.5,7 Z M13.5,15 L18.5,15 C19.3284271,15 20,15.6715729 20,16.5 C20,17.3284271 19.3284271,18 18.5,18 L13.5,18 C12.6715729,18 12,17.3284271 12,16.5 C12,15.6715729 12.6715729,15 13.5,15 Z" fill="currentColor" opacity="0.3"></path>
                          <path d="M17,11 C15.3431458,11 14,9.65685425 14,8 C14,6.34314575 15.3431458,5 17,5 C18.6568542,5 20,6.34314575 20,8 C20,9.65685425 18.6568542,11 17,11 Z M6,19 C4.34314575,19 3,17.6568542 3,16 C3,14.3431458 4.34314575,13 6,13 C7.65685425,13 9,14.3431458 9,16 C9,17.6568542 7.65685425,19 6,19 Z" fill="currentColor"></path>
                        </g>
                      </svg>
                    </span>
                    <span className="grow">Configuración</span>
                  </a>
                </li>
              </ul>
            </div>
            <div className="hidden lg:flex items-center ms-auto">
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <div className="shrink">
                    <div className="h-8 w-8 me-2">
                      <div className="avatar h-full w-full rounded-full me-2 bg-primary/10 flex items-center justify-center">
                        <span className="text-primary font-medium text-sm">
                          {user.name ? user.name.charAt(0).toUpperCase() : (user.email ? user.email.charAt(0).toUpperCase() : 'U')}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="grow ms-1 leading-normal">
                    <span className="block text-sm font-medium">{user.name || user.email}</span>
                    <span className="block text-gray-400 text-xs text-left">{user.role}</span>
                  </div>
                </div>
                <button
                  onClick={() => signOut({ callbackUrl: "/" })}
                  className="text-sm bg-white border border-gray-200 hover:bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md transition-colors flex items-center"
                  title="Cerrar sesión"
                >
                  <span className="hidden lg:inline xl:hidden">Salir</span>
                  <span className="inline lg:hidden xl:inline">Cerrar sesión</span>
                </button>
              </div>
            </div>
            <div className="block grow ms-auto lg:shrink me-4 lg:me-0 lg:hidden">
              <ul className="navbar-nav flex gap-x-3 items-center justify-end lg:justify-center">
                <li className="nav-item">
                  <div className="relative">
                    <button className="nav-link after:absolute hover:after:-bottom-10 after:inset-0" type="button">
                      <div className="flex items-center">
                        <div className="shrink">
                          <div className="h-8 w-8 me-2">
                            <div className="avatar h-full w-full rounded-full me-2 bg-primary/10 flex items-center justify-center">
                              <span className="text-primary font-medium text-sm">
                                {user.name ? user.name.charAt(0).toUpperCase() : (user.email ? user.email.charAt(0).toUpperCase() : 'U')}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </button>
                  </div>
                </li>
                <li className="nav-item">
                  <button
                    onClick={() => signOut({ callbackUrl: "/" })}
                    className="nav-link flex items-center font-medium py-2 px-3 text-gray-700 hover:text-red-600 transition-all"
                    title="Cerrar sesión"
                  >
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                  </button>
                </li>
              </ul>
            </div>
            <div className="lg:hidden flex items-center ms-auto px-2.5">
              <button
                type="button"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M0 96C0 78.3 14.3 64 32 64H416c17.7 0 32 14.3 32 32s-14.3 32-32 32H32C14.3 128 0 113.7 0 96zM0 256c0-17.7 14.3-32 32-32H416c17.7 0 32 14.3 32 32s-14.3 32-32 32H32c-17.7 0-32-14.3-32-32zM448 416c0 17.7-14.3 32-32 32H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H416c17.7 0 32 14.3 32 32z"></path>
                </svg>
              </button>
            </div>
          </nav>
        </div>
      </header>

      <div className="relative mt-[110px]">
      <div className="flex justify-center">
        <div className="flex max-w-7xl w-full">
          {/* Sidebar - Pegado al container pero fuera */}
          <div className={`
            lg:w-56 bg-white shadow-md border border-gray-200 rounded-lg h-fit lg:ml-6 lg:mt-6 lg:mr-0
            ${sidebarOpen ? 'block' : 'hidden'} lg:block
            fixed lg:sticky lg:top-6 z-20 w-full lg:w-56
          `}>
          {/* Header del Sidebar */}
          <div className="p-4 border-b border-gray-100">
            <div>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                user.role === "ADMIN" ? "bg-purple-100 text-purple-800" :
                user.role === "SUPER_ADMIN" ? "bg-red-100 text-red-800" :
                user.role === "WORKER" ? "bg-green-100 text-green-800" :
                "bg-blue-100 text-blue-800"
              }`}>
                {user.role}
              </span>
              <p className="text-sm text-gray-500 truncate mt-1">Bienvenido {user.name || user.email}</p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="p-3" aria-label="Sidebar Navigation">
            {/* Título KPIs */}
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-3">
              KPIs
            </h3>
            <div className="space-y-1">
              {tabs.map((tab) => {
                const isActive = activeTab === tab.id;

                return (
                  <button
                    key={tab.id}
                    className={`
                      text-start py-2 px-3 rounded-md transition-all w-full flex items-center gap-2
                      ${isActive
                        ? "bg-primary/10 text-primary"
                        : "bg-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                      }
                    `}
                    onClick={() => {
                      setActiveTab(tab.id);
                      setSidebarOpen(false);
                    }}
                    role="tab"
                    aria-selected={isActive}
                    aria-controls={`${tab.id}-panel`}
                  >
                    <tab.icon className={`h-4 w-4 ${isActive ? 'text-primary' : 'text-gray-500'}`} />
                    <span className="text-sm font-medium">{tab.label}</span>
                  </button>
                );
              })}
            </div>
          </nav>

        </div>

        {/* Overlay para móvil */}
        {sidebarOpen && (
          <div
            className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-15"
            onClick={() => setSidebarOpen(false)}
          />
        )}

          {/* Main Content - Con container que ocupa todo el espacio */}
          <div className="flex-1 min-w-0 lg:py-6 lg:pr-6 py-6 px-4">
            <div className="w-full lg:ml-0 mx-auto">
            {/* Mobile menu button */}
            <div className="lg:hidden bg-white shadow-lg border border-gray-200 p-4 rounded-lg mb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Activity className="h-5 w-5 text-primary" />
                  </div>
                  <h2 className="text-xl font-medium text-gray-900">Panel Admin</h2>
                </div>
                <button
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
                >
                  {sidebarOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                </button>
              </div>
            </div>
          {activeTab === "ventas" && (
            <div className="space-y-8">

              {/* Mostrar formulario inline si showInlineForm es true */}
              {showInlineForm ? (
                <KpiSemanalInlineForm
                  onClose={() => setShowInlineForm(false)}
                  onSave={handleSaveKpi}
                  editingKpi={editingKpi}
                  isAddingOldWeek={isAddingOldWeek}
                  existingKpis={kpisSemanales}
                />
              ) : showKpiHistory ? (
                <div className="space-y-6">
                  {/* Header del historial */}
                  <div className="flex flex-col sm:flex-row justify-between items-center">
                    <div className="mb-3 sm:mb-0">
                      <h1 className="text-2xl font-medium text-gray-900">
                        Historial de Semanas - Ventas
                      </h1>
                      <div className="flex items-center mt-1">
                        <div className="flex items-center space-x-3">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {kpisSemanales.length} registros
                          </span>
                        </div>
                        <p className="text-sm text-gray-500 ml-3">Tabla de rendimiento comercial y métricas semanales</p>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 lg:gap-3">
                      <div className="flex items-center bg-white rounded-lg shadow-sm border border-gray-200 p-1">
                        <button
                          onClick={() => {
                            // Exportar datos de ventas
                            const headers = [
                              'Semana', 'Año', 'Fecha Inicio', 'Fecha Fin', 'Volumen (L)',
                              'Crecimiento (%)', 'Margen Bruto', 'Retención (%)', 'Cumplimiento (%)',
                              'Desviación (%)', 'Ciclo Cierre', 'Clientes Activos', 'Usuario', 'Fecha Creación'
                            ];

                            const csvContent = "data:text/csv;charset=utf-8," +
                              headers.join(",") + "\n" +
                              kpisSemanales.map(kpi => [
                                kpi.weekNumber,
                                kpi.year,
                                new Date(kpi.weekStartDate).toLocaleDateString('es-ES'),
                                new Date(kpi.weekEndDate).toLocaleDateString('es-ES'),
                                kpi.volumenVenta,
                                kpi.crecimientoVentas,
                                kpi.margenBruto,
                                kpi.retencionClientes,
                                kpi.cumplimientoObjetivo,
                                kpi.desviacionPresupuesto,
                                kpi.cicloPromedioVenta,
                                kpi.clientesActivos,
                                `"${(kpi as any).user?.name || (kpi as any).user?.email || 'Usuario desconocido'}"`,
                                (kpi as any).createdAt ? new Date((kpi as any).createdAt).toLocaleDateString('es-ES') : ''
                              ].join(",")).join("\n");

                            const encodedUri = encodeURI(csvContent);
                            const link = document.createElement("a");
                            link.setAttribute("href", encodedUri);
                            link.setAttribute("download", `kpis_ventas_${new Date().toISOString().split('T')[0]}.csv`);
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }}
                          className="p-2 text-gray-500 hover:text-primary hover:bg-primary/5 rounded-md transition-all duration-200"
                          title="Exportar todos los datos"
                        >
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                          </svg>
                        </button>
                        {/* Solo mostrar botones de Analytics y Filtros si hay KPIs registrados */}
                        {kpisSemanales.length > 0 && (
                          <>
                            <div className="w-px h-6 bg-gray-200 mx-1"></div>
                            <button
                              onClick={() => setShowAnalytics(!showAnalytics)}
                              className={`p-2 rounded-md transition-all duration-200 ${
                                showAnalytics
                                  ? 'text-primary bg-primary/10 shadow-sm'
                                  : 'text-gray-500 hover:text-primary hover:bg-primary/5'
                              }`}
                              title="Mostrar/Ocultar Analytics"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-eye h-4 w-4">
                                <path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                              </svg>
                            </button>
                            <div className="w-px h-6 bg-gray-200 mx-1"></div>
                            <button
                              onClick={() => setShowFilters(!showFilters)}
                              className="p-2 rounded-md transition-all duration-200 text-gray-500 hover:text-primary hover:bg-primary/5"
                              title="Filtros"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-filter h-4 w-4">
                                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                              </svg>
                            </button>
                          </>
                        )}
                        <div className="w-px h-6 bg-gray-200 mx-1"></div>
                        <button
                          onClick={handleAddOldWeek}
                          className="p-2 text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-all duration-200"
                          title="Agregar Semana Antigua"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-plus h-4 w-4">
                            <path d="M5 12h14"></path>
                            <path d="M12 5v14"></path>
                          </svg>
                        </button>
                      </div>
                      <button
                        onClick={() => setShowKpiHistory(false)}
                        className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                        title="Volver al Dashboard"
                      >
                        <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                      </button>
                    </div>
                  </div>

                  {/* Contenido del historial */}
                  <KpiHistorySection
                    kpisSemanales={kpisSemanales}
                    loadingKpis={loadingKpis}
                    user={user}
                    onEditKpi={handleEditKpi}
                    onDeleteKpi={handleDeleteKpi}
                    onRefresh={loadKpisSemanales}
                    showAnalytics={showAnalytics}
                    showFilters={showFilters}
                    selectedKpis={selectedKpis}
                    onSelectionChange={setSelectedKpis}
                  />
                </div>
              ) : (
                <>

              {/* Fila con título a la izquierda y selectores a la derecha */}
              <div className="flex flex-col sm:flex-row justify-between items-center mb-4">
                <div className="mb-3 sm:mb-0">
                  <h1 className="text-2xl font-medium text-gray-900">
                    Panel de KPIs de Ventas
                  </h1>
                  <div className="flex items-center mt-1">
                    <span className={`text-xs md:text-xs lg:text-sm rounded-full px-3 py-1 ${
                      hasRealData
                        ? 'bg-green-500/10 text-green-600'
                        : 'bg-yellow-500/10 text-yellow-600'
                    }`}>
                      {hasRealData ? 'Datos Reales Capturados' : 'Datos de Ejemplo'}
                    </span>
                    {hasRealData && kpisSemanales.length > 0 && (
                      <span className="ml-2 text-sm text-gray-500">
                        Última actualización: Semana {kpisSemanales[0].weekNumber}/{kpisSemanales[0].year}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 lg:gap-3">
                  <div className="flex items-center gap-2">
                    <div className="inline-flex rounded-md shadow-sm" role="group">
                      <button
                        type="button"
                        className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                          timeRange === "4w"
                            ? "bg-primary text-white border-primary"
                            : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                        } rounded-l-lg focus:z-10 focus:ring-2 focus:ring-primary`}
                        onClick={() => setTimeRange("4w")}
                      >
                        1m
                      </button>
                      <button
                        type="button"
                        className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                          timeRange === "12w"
                            ? "bg-primary text-white border-primary"
                            : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                        } focus:z-10 focus:ring-2 focus:ring-primary`}
                        onClick={() => setTimeRange("12w")}
                      >
                        3m
                      </button>
                      <button
                        type="button"
                        className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                          timeRange === "24w"
                            ? "bg-primary text-white border-primary"
                            : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                        } rounded-r-lg focus:z-10 focus:ring-2 focus:ring-primary`}
                        onClick={() => setTimeRange("24w")}
                      >
                        6m
                      </button>
                    </div>

                    {/* Botón de descarga PDF junto a los botones de tiempo */}
                    <button
                      type="button"
                      onClick={downloadAllChartsAsPDF}
                      disabled={loadingPdfDownload}
                      className={`p-2 text-primary border border-transparent rounded-md focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors flex items-center justify-center ${
                        loadingPdfDownload
                          ? 'bg-primary/5 cursor-not-allowed'
                          : 'bg-primary/10 hover:bg-primary/20'
                      }`}
                      title={loadingPdfDownload ? "Generando PDF..." : "Descargar todas las gráficas como PDF"}
                    >
                      {loadingPdfDownload ? (
                        <svg className="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      ) : (
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                      )}
                    </button>
                  </div>

                  {/* Botones de Gestión de KPIs */}
                  {user.role && (user.role === "ADMIN" || user.role === "SUPER_ADMIN" || user.role === "WORKER") && (
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1.5 lg:gap-2">
                      <button
                        type="button"
                        onClick={handleNewKpi}
                        className="p-2 text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                        title="Agregar Datos"
                      >
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      </button>

                      <div className="text-end">
                        <button
                          type="button"
                          onClick={() => setShowKpiHistory(!showKpiHistory)}
                          className="inline-flex gap-1 items-center font-semibold text-primary text-sm hover:text-primary/80 transition-colors"
                        >
                          Ver más
                          <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg">
                            <path d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"></path>
                          </svg>
                        </button>
                      </div>

                    </div>
                  )}
                </div>
              </div>

              {/* Primera Fila - 3 Columnas Compactas */}
              <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6 my-3">
                {/* 1. Volumen Total de Venta por Mes */}
                <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-blue-500/10 flex items-center justify-center mr-3">
                        <Droplet className="h-5 w-5 text-blue-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">VOLUMEN TOTAL DE VENTA (LITROS)</h3>
                          <CalculationButton
                            kpiId="volumen"
                            calculation="Suma total de litros vendidos en el mes"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          {(indicadoresActuales.volumenTotalLitros / 1000).toLocaleString(undefined, { maximumFractionDigits: 1 })} K L
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Volumen (Litros)',
                            data: evolucionActual.map(item => Number(item.volumen.toFixed(0))),
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#3b82f6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          layout: getChartLayout(),
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${(context.parsed.y / 1000).toFixed(1)}K Litros`
                              }
                            },
                            datalabels: getSmartDataLabelsConfig((value: any) => `${(value / 1000).toFixed(0)}K`)
                          },
                          // Core options
                          aspectRatio: 5 / 3,
                          elements: {
                            line: {
                              fill: false,
                              tension: 0.4
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              stacked: true,
                              title: {
                                display: true,
                                text: 'Volumen (Litros)'
                              },
                              ticks: {
                                callback: (value: any) => `${(value / 1000).toFixed(0)}K`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución del Volumen Total de Venta', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Volumen',
                            data: evolucionActual.map(item => Number(item.volumen.toFixed(0))),
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#3b82f6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${(context.parsed.y / 1000).toLocaleString(undefined, { maximumFractionDigits: 1 })} K L`
                              }
                            },
                            datalabels: {
                              display: false // Ocultar en gráficas pequeñas
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          },
                          elements: {
                            point: { radius: 3 }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* 2. Crecimiento Mensual de Ventas */}
                <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-green-500/10 flex items-center justify-center mr-3">
                        <TrendingUp className="h-5 w-5 text-green-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">CRECIMIENTO DE VENTAS (%)</h3>
                          <CalculationButton
                            kpiId="crecimiento"
                            calculation="[(Ventas mes actual - Ventas mes anterior) / Ventas mes anterior] * 100"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          +{indicadoresActuales.crecimientoMensual}%
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Crecimiento (%)',
                            data: evolucionActual.map(item => Number(item.crecimiento.toFixed(2))),
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#10b981',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          layout: getChartLayout(),
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.parsed.y.toFixed(2)}% de crecimiento`
                              }
                            },
                            datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(2)}%`)
                          },
                          // Core options
                          aspectRatio: 5 / 3,
                          elements: {
                            line: {
                              fill: false,
                              tension: 0.4
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              stacked: true,
                              title: {
                                display: true,
                                text: 'Crecimiento (%)'
                              },
                              ticks: {
                                callback: (value: any) => `${Number(value).toFixed(2)}%`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución del Crecimiento de Ventas', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Crecimiento',
                            data: evolucionActual.map(item => Number(item.crecimiento.toFixed(2))),
                            borderColor: '#10b981',
                            backgroundColor: '#10b981',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#10b981',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed.y.toFixed(2)}%`
                              }
                            },
                            datalabels: {
                              display: false // Ocultar en gráficas pequeñas
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* 3. Margen Bruto por Litro Vendido */}
                <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-yellow-500/10 flex items-center justify-center mr-3">
                        <DollarSign className="h-5 w-5 text-yellow-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">MARGEN BRUTO POR LITRO VENDIDO</h3>
                          <CalculationButton
                            kpiId="margen"
                            calculation="(Ingreso total - Costo total) / Litros vendidos"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          ${indicadoresActuales.margenBrutoPorLitro}
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Margen Bruto por Litro ($)',
                            data: evolucionActual.map(item => Number(item.margen.toFixed(2))),
                            borderColor: '#f59e0b',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#f59e0b',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          layout: getChartLayout(),
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `$${Number(context.parsed.y).toFixed(2)} por litro`
                              }
                            },
                            datalabels: getSmartDataLabelsConfig((value: any) => `$${Number(value).toFixed(2)}`)
                          },
                          // Core options
                          aspectRatio: 5 / 3,
                          elements: {
                            line: {
                              fill: false,
                              tension: 0.4
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              stacked: true,
                              title: {
                                display: true,
                                text: 'Margen Bruto ($)'
                              },
                              ticks: {
                                callback: (value: any) => `$${Number(value).toFixed(2)}`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución del Margen Bruto por Litro', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Margen',
                            data: evolucionActual.map(item => Number(item.margen.toFixed(2))),
                            borderColor: '#f59e0b',
                            backgroundColor: '#f59e0b',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#f59e0b',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `$${context.parsed.y.toFixed(2)}`
                              }
                            },
                            datalabels: {
                              display: false // Ocultar en gráficas pequeñas
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Segunda Fila - 1 Columna Grande */}
              <div className="my-2">
                {/* 4. Tasa de Retención de Clientes - GRANDE */}
                <div className="bg-white shadow-md border border-gray-200 rounded-lg p-4 relative">
                  <div className="relative">
                    <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-end-8 after:-z-10 after:bg-[url('../assets/images/pattern/dot5.svg')] hidden sm:block" />
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-purple-500/10 flex items-center justify-center mr-3">
                          <Users className="h-5 w-5 text-purple-500" />
                        </div>
                        <div>
                          <div className="flex items-center">
                            <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">TASA DE RETENCIÓN DE CLIENTES</h3>
                            <CalculationButton
                              kpiId="retencion"
                              calculation="(Clientes retenidos / Clientes del periodo anterior) * 100"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresActuales.tasaRetencionClientes}%
                        </div>
                        <p className="text-xs text-gray-400">Tasa actual</p>
                      </div>
                    </div>
                  </div>
                  <div className="grid lg:grid-cols-2 gap-4">
                    <div
                      style={{ width: '100%', height: '150px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: distribucionRetencionActual.map(item => item.name),
                          datasets: [{
                            data: distribucionRetencionActual.map(item => item.value),
                            backgroundColor: distribucionRetencionActual.map(item => item.color),
                            borderWidth: 2,
                            borderColor: '#ffffff',
                            hoverBorderWidth: 3,
                            hoverBorderColor: '#ffffff'
                          }]
                        };
                        const chartOptions = {
                          ...getDoughnutChartOptions(),
                          animation: {
                            duration: 1200,
                            easing: 'easeInOutQuart'
                          },
                          cutout: '40%',
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            ...getDoughnutChartOptions().plugins,
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.label}: ${context.parsed}%`
                              }
                            }
                          }
                        };
                        openChartDialog('Distribución de la Tasa de Retención de Clientes', chartData, chartOptions, 'doughnut');
                      }}
                    >
                      <Doughnut
                        data={{
                          labels: distribucionRetencionActual.map(item => item.name),
                          datasets: [{
                            data: distribucionRetencionActual.map(item => item.value),
                            backgroundColor: distribucionRetencionActual.map(item => item.color),
                            borderWidth: 0
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1200,
                            easing: 'easeInOutQuart'
                          },
                          cutout: '50%',
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed}%`
                              }
                            },
                            datalabels: {
                              display: false // Ocultar etiquetas en gráfica pequeña
                            }
                          }
                        }}
                      />
                    </div>
                    <div
                      style={{ width: '100%', height: '150px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Tasa de Retención (%)',
                            data: evolucionActual.map(item => Number(item.retencion.toFixed(2))),
                            borderColor: '#8b5cf6',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#8b5cf6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          layout: getChartLayout(),
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.parsed.y.toFixed(2)}% de retención`
                              }
                            },
                            datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(2)}%`)
                          },
                          // Core options
                          aspectRatio: 5 / 3,
                          elements: {
                            line: {
                              fill: false,
                              tension: 0.4
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              stacked: true,
                              title: {
                                display: true,
                                text: 'Tasa de Retención (%)'
                              },
                              ticks: {
                                callback: (value: any) => `${Number(value).toFixed(2)}%`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución de la Tasa de Retención de Clientes', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Retención',
                            data: evolucionActual.map(item => Number(item.retencion.toFixed(2))),
                            borderColor: '#8b5cf6',
                            backgroundColor: '#8b5cf6',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#8b5cf6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed.y}%`
                              }
                            },
                            datalabels: {
                              display: false // Ocultar etiquetas en gráfica pequeña
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Tercera Fila - 3 Columnas Compactas */}
              <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6 my-6">
                {/* 5. Porcentaje de Cumplimiento del Objetivo */}
                <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-indigo-500/10 flex items-center justify-center mr-3">
                        <Target className="h-5 w-5 text-indigo-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">CUMPLIMIENTO DEL OBJETIVO DE VENTAS (%)</h3>
                          <CalculationButton
                            kpiId="cumplimiento"
                            calculation="(Ventas reales / Meta de ventas) * 100"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresActuales.cumplimientoObjetivo}%
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: distribucionCumplimientoActual.map(item => item.name),
                          datasets: [{
                            data: distribucionCumplimientoActual.map(item => item.value),
                            backgroundColor: distribucionCumplimientoActual.map(item => item.color),
                            borderWidth: 2,
                            borderColor: '#ffffff',
                            hoverBorderWidth: 3,
                            hoverBorderColor: '#ffffff'
                          }]
                        };
                        const chartOptions = {
                          ...getDoughnutChartOptions(),
                          animation: {
                            duration: 1200,
                            easing: 'easeInOutQuart'
                          },
                          cutout: '50%',
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            ...getDoughnutChartOptions().plugins,
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.label}: ${context.parsed}%`
                              }
                            }
                          }
                        };
                        openChartDialog('Distribución del Cumplimiento de Objetivos', chartData, chartOptions, 'doughnut');
                      }}
                    >
                      <Doughnut
                        data={{
                          labels: distribucionCumplimientoActual.map(item => item.name),
                          datasets: [{
                            data: distribucionCumplimientoActual.map(item => item.value),
                            backgroundColor: distribucionCumplimientoActual.map(item => item.color),
                            borderWidth: 0
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1200,
                            easing: 'easeInOutQuart'
                          },
                          cutout: '60%',
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed}%`
                              }
                            },
                            datalabels: {
                              display: false // Ocultar etiquetas en gráfica pequeña
                            }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* 6. Desviación entre Ventas Proyectadas y Reales */}
                <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-orange-500/10 flex items-center justify-center mr-3">
                        <Activity className="h-5 w-5 text-orange-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">DESVIACIÓN ENTRE VENTAS PROYECTADAS Y REALES</h3>
                          <CalculationButton
                            kpiId="desviacion"
                            calculation="(Ventas reales - Ventas proyectadas) / Ventas proyectadas * 100"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresActuales.desviacionVentas}%
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Desviación (%)',
                            data: evolucionActual.map(item => Number(item.desviacion.toFixed(2))),
                            borderColor: '#f97316',
                            backgroundColor: 'rgba(249, 115, 22, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#f97316',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          layout: getChartLayout(),
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.parsed.y.toFixed(2)}% de desviación`
                              }
                            },
                            datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(2)}%`)
                          },
                          // Core options
                          aspectRatio: 5 / 3,
                          elements: {
                            line: {
                              fill: false,
                              tension: 0.4
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              stacked: true,
                              title: {
                                display: true,
                                text: 'Desviación (%)'
                              },
                              ticks: {
                                callback: (value: any) => `${Number(value).toFixed(2)}%`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución de la Desviación entre Ventas Proyectadas y Reales', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Desviación',
                            data: evolucionActual.map(item => Number(item.desviacion.toFixed(2))),
                            borderColor: '#f97316',
                            backgroundColor: '#f97316',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#f97316',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed.y}%`
                              }
                            },
                            datalabels: {
                              display: false // Ocultar etiquetas en gráfica pequeña
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* 7. Ciclo Promedio de Cierre de Ventas */}
                <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-teal-500/10 flex items-center justify-center mr-3">
                        <Activity className="h-5 w-5 text-teal-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">CICLO PROMEDIO DE CIERRE DE VENTAS (DÍAS)</h3>
                          <CalculationButton
                            kpiId="ciclo"
                            calculation="Promedio de días entre contacto inicial y cierre"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresActuales.cicloPromedioCierre} días
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Ciclo Promedio de Cierre (días)',
                            data: evolucionActual.map(item => Number(item.ciclo.toFixed(0))),
                            borderColor: '#14b8a6',
                            backgroundColor: 'rgba(20, 184, 166, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#14b8a6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          layout: getChartLayout(),
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.parsed.y.toFixed(0)} días promedio`
                              }
                            },
                            datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(0)}d`)
                          },
                          // Core options
                          aspectRatio: 5 / 3,
                          elements: {
                            line: {
                              fill: false,
                              tension: 0.4
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              stacked: true,
                              title: {
                                display: true,
                                text: 'Días Promedio'
                              },
                              ticks: {
                                callback: (value: any) => `${Number(value).toFixed(0)} días`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución del Ciclo Promedio de Cierre de Ventas', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Ciclo',
                            data: evolucionActual.map(item => Number(item.ciclo.toFixed(0))),
                            borderColor: '#14b8a6',
                            backgroundColor: '#14b8a6',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#14b8a6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed.y} días`
                              }
                            },
                            datalabels: {
                              display: false // Ocultar en gráficas pequeñas
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Cuarta Fila - 1 Columna Grande */}
              <div className="my-3">
                {/* 8. Número de Clientes Activos - GRANDE */}
                <div className="bg-white shadow-md border border-gray-200 rounded-lg p-4 relative">
                  <div className="relative">
                    <div className="before:w-20 before:h-20 before:absolute before:-bottom-12 before:-start-12 before:-z-10 before:bg-[url('../assets/images/pattern/dot2.svg')] hidden sm:block" />
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-emerald-500/10 flex items-center justify-center mr-3">
                          <Users className="h-5 w-5 text-emerald-500" />
                        </div>
                        <div>
                          <div className="flex items-center">
                            <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">NÚMERO DE CLIENTES ACTIVOS</h3>
                            <CalculationButton
                              kpiId="clientesActivos"
                              calculation="Clientes con al menos una compra en el mes"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresActuales.clientesActivosMensuales.toLocaleString()}
                        </div>
                        <p className="text-xs text-gray-400">Clientes activos</p>
                      </div>
                    </div>
                  </div>
                  <div
                    style={{ width: '100%', height: '170px', cursor: 'pointer' }}
                    onClick={() => {
                      const chartData = {
                        labels: evolucionActual.map(item => item.mes),
                        datasets: [{
                          label: 'Clientes Activos',
                          data: evolucionActual.map(item => Number(item.clientesActivos.toFixed(0))),
                          borderColor: '#10b981',
                          backgroundColor: 'rgba(16, 185, 129, 0.1)',
                          borderWidth: 3,
                          fill: true,
                          tension: 0.4,
                          pointRadius: 5,
                          pointHoverRadius: 7,
                          pointBackgroundColor: '#10b981',
                          pointBorderColor: '#ffffff',
                          pointBorderWidth: 2
                        }]
                      };
                      const chartOptions = {
                        layout: getChartLayout(),
                        animation: {
                          duration: 1000,
                          easing: 'easeInOutQuart'
                        },
                        interaction: {
                          intersect: false,
                          mode: 'nearest'
                        },
                        plugins: {
                          tooltip: {
                            callbacks: {
                              label: (context: any) => `${context.parsed.y.toLocaleString()} clientes`
                            }
                          },
                          datalabels: getSmartDataLabelsConfig((value: any) => value.toLocaleString())
                        },
                        // Core options
                        aspectRatio: 5 / 3,
                        elements: {
                          line: {
                            fill: false,
                            tension: 0.4
                          }
                        },
                        scales: {
                          x: {
                            display: true,
                            title: {
                              display: true,
                              text: 'Período'
                            }
                          },
                          y: {
                            display: true,
                            stacked: true,
                            title: {
                              display: true,
                              text: 'Número de Clientes'
                            },
                            ticks: {
                              callback: (value: any) => value.toLocaleString()
                            }
                          }
                        }
                      };
                      openChartDialog('Evolución del Número de Clientes Activos', chartData, chartOptions, 'line');
                    }}
                  >
                    <Line
                      data={{
                        labels: evolucionActual.map(item => item.mes),
                        datasets: [{
                          label: 'Clientes Activos',
                          data: evolucionActual.map(item => Number(item.clientesActivos.toFixed(0))),
                          borderColor: '#10b981',
                          backgroundColor: 'rgba(16, 185, 129, 0.1)',
                          borderWidth: 3,
                          fill: true,
                          tension: 0.4,
                          pointRadius: 4,
                          pointHoverRadius: 6,
                          pointBackgroundColor: '#10b981',
                          pointBorderColor: '#ffffff',
                          pointBorderWidth: 2
                        }]
                      }}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: {
                          duration: 1000,
                          easing: 'easeInOutQuart'
                        },
                        interaction: {
                          intersect: false,
                          mode: 'nearest'
                        },
                        plugins: {
                          legend: { display: false },
                          tooltip: {
                            animation: {
                              duration: 200
                            },
                            callbacks: {
                              label: (context) => `${context.parsed.y.toLocaleString()}`
                            }
                          },
                          datalabels: {
                            display: false // Ocultar en gráficas pequeñas
                          }
                        },
                        scales: {
                          x: { display: false },
                          y: { display: false }
                        }
                      }}
                    />
                  </div>
                </div>
              </div>

              </>
              )}

            </div>
          )}

          {activeTab === "compras" && (
            <AdminDashboardCompras
              user={user}
              openChartDialog={openChartDialog}
              getSmartDataLabelsConfig={getSmartDataLabelsConfig}
              getChartLayout={getChartLayout}
              getDoughnutDataLabelsConfig={getDoughnutDataLabelsConfig}
              getDoughnutChartOptions={getDoughnutChartOptions}
              kpisCompras={kpisCompras}
              loadingKpisCompras={loadingKpisCompras}
            />
          )}

          {activeTab === "logistica" && (
            <div className="space-y-8">

              {/* Mostrar formulario inline si showInlineFormLogistica es true */}
              {showInlineFormLogistica ? (
                <KpiLogisticaInlineForm
                  onClose={() => setShowInlineFormLogistica(false)}
                  onSave={handleSaveKpiLogistica}
                  editingKpi={editingKpiLogistica}
                  isAddingOldWeek={isAddingOldWeekLogistica}
                  existingKpis={kpisLogistica}
                />
              ) : (
                <>

              {/* Fila con título a la izquierda y selectores a la derecha */}
              <div className="flex flex-col sm:flex-row justify-between items-center mb-4">
                <div className="mb-3 sm:mb-0">
                  <h1 className="text-2xl font-medium text-gray-900">
                    Panel de KPIs de Logística
                  </h1>
                  <div className="flex items-center mt-1">
                    <span className={`text-xs md:text-xs lg:text-sm rounded-full px-3 py-1 ${
                      kpisLogistica.length > 0
                        ? 'bg-green-500/10 text-green-600'
                        : 'bg-yellow-500/10 text-yellow-600'
                    }`}>
                      {kpisLogistica.length > 0 ? 'Datos Reales Capturados' : 'Datos de Ejemplo'}
                    </span>
                    {kpisLogistica.length > 0 && (
                      <span className="ml-2 text-sm text-gray-500">
                        Última actualización: Semana {kpisLogistica[0].weekNumber}/{kpisLogistica[0].year}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 lg:gap-3">
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                      type="button"
                      className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                        timeRangeLogistica === "4w"
                          ? "bg-primary text-white border-primary"
                          : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                      } rounded-l-lg focus:z-10 focus:ring-2 focus:ring-primary`}
                      onClick={() => setTimeRangeLogistica("4w")}
                    >
                      1m
                    </button>
                    <button
                      type="button"
                      className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                        timeRangeLogistica === "12w"
                          ? "bg-primary text-white border-primary"
                          : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                      } focus:z-10 focus:ring-2 focus:ring-primary`}
                      onClick={() => setTimeRangeLogistica("12w")}
                    >
                      3m
                    </button>
                    <button
                      type="button"
                      className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                        timeRangeLogistica === "24w"
                          ? "bg-primary text-white border-primary"
                          : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                      } rounded-r-lg focus:z-10 focus:ring-2 focus:ring-primary`}
                      onClick={() => setTimeRangeLogistica("24w")}
                    >
                      6m
                    </button>
                  </div>

                  {/* Botones de Gestión de KPIs */}
                  {user.role && (user.role === "ADMIN" || user.role === "SUPER_ADMIN" || user.role === "WORKER") && (
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1.5 lg:gap-2">
                      <button
                        type="button"
                        onClick={handleNewKpiLogistica}
                        className="p-2 text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                        title="Agregar Datos"
                      >
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      </button>

                      <button
                        type="button"
                        onClick={handleAddOldWeekLogistica}
                        className="p-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors flex items-center justify-center"
                        title="Agregar Semana Anterior"
                      >
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </button>

                      {/* Botón de descarga PDF de gráficas */}
                      <button
                        type="button"
                        onClick={downloadAllChartsAsPDF}
                        disabled={loadingPdfDownload}
                        className={`p-2 text-white border border-transparent rounded-md focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center justify-center ${
                          loadingPdfDownload
                            ? 'bg-blue-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                        title={loadingPdfDownload ? "Generando PDF..." : "Descargar todas las gráficas como PDF"}
                      >
                        {loadingPdfDownload ? (
                          <svg className="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        ) : (
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                          </svg>
                        )}
                      </button>

                      <div className="w-px h-6 bg-gray-200 mx-1"></div>
                      <button
                        onClick={() => setShowAnalyticsLogistica(!showAnalyticsLogistica)}
                        className={`p-2 rounded-md transition-all duration-200 ${
                          showAnalyticsLogistica
                            ? 'text-primary bg-primary/10 shadow-sm'
                            : 'text-gray-500 hover:text-primary hover:bg-primary/5'
                        }`}
                        title="Mostrar/Ocultar Analytics"
                      >
                        <Eye className="h-4 w-4" />
                      </button>

                    </div>
                  )}
                </div>
              </div>
              {/* Mensaje cuando no hay datos */}
              {kpisLogistica.length === 0 && (
                <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                  <div className="h-12 w-12 rounded-md bg-orange-500/10 flex items-center justify-center mb-4">
                    <Truck className="h-6 w-6 text-orange-500" />
                  </div>
                  <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-4">Panel de Logística</h3>
                  <p className="text-base md:text-base lg:text-lg text-gray-500">
                    No hay datos de logística disponibles. Agregue datos para comenzar a visualizar los KPIs.
                  </p>
                </div>
              )}

              {/* Gráficas de Logística cuando hay datos */}
              {filteredKpisLogistica.length > 0 && (() => {
                // Calcular indicadores actuales de logística
                const kpiActual = kpisLogistica[0];
                const indicadoresLogisticaActuales = {
                  unidadesConfirmadas: kpiActual.unidadesConfirmadas,
                  unidadesSolicitadas: kpiActual.unidadesSolicitadas,
                  porcentajeEntregasTiempo: kpiActual.porcentajeEntregasTiempo,
                  porcentajeRetardos: kpiActual.porcentajeRetardos,
                  porcentajeReprogramaciones: kpiActual.porcentajeReprogramaciones,
                  promedioKmOperacion: kpiActual.promedioKmOperacion,
                  promedioCostoFleteLitro: kpiActual.promedioCostoFleteLitro,
                  promedioCostoFleteOperacion: kpiActual.promedioCostoFleteOperacion,
                  pagoSemanalFlete: kpiActual.pagoSemanalFlete,
                  pagoSemanalPenalizaciones: kpiActual.pagoSemanalPenalizaciones,
                  porcentajeRutasCotizadas: kpiActual.porcentajeRutasCotizadas,
                  porcentajeTransportistas: kpiActual.porcentajeTransportistas
                };

                // Calcular evolución de logística usando datos filtrados por temporalidad
                const evolucionLogistica = filteredKpisLogistica.slice().reverse().map((kpi: any) => ({
                  mes: formatWeekDatesForChart(kpi.year, kpi.weekNumber),
                  unidadesConfirmadas: kpi.unidadesConfirmadas,
                  unidadesSolicitadas: kpi.unidadesSolicitadas,
                  porcentajeEntregasTiempo: kpi.porcentajeEntregasTiempo,
                  porcentajeRetardos: kpi.porcentajeRetardos,
                  porcentajeReprogramaciones: kpi.porcentajeReprogramaciones,
                  promedioKmOperacion: kpi.promedioKmOperacion,
                  promedioCostoFleteLitro: kpi.promedioCostoFleteLitro,
                  promedioCostoFleteOperacion: kpi.promedioCostoFleteOperacion,
                  pagoSemanalFlete: kpi.pagoSemanalFlete,
                  pagoSemanalPenalizaciones: kpi.pagoSemanalPenalizaciones,
                  porcentajeRutasCotizadas: kpi.porcentajeRutasCotizadas,
                  porcentajeTransportistas: kpi.porcentajeTransportistas
                }));

                return (
                  <>
                    {/* Primera Fila - 3 Columnas */}
                    <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6 my-3">
                      {/* 1. Unidades Confirmadas vs Solicitadas */}
                      <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                        <div className="p-6">
                          <div className="flex items-center mb-3">
                            <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-blue-500/10 flex items-center justify-center mr-3">
                              <Package className="h-5 w-5 text-blue-500" />
                            </div>
                            <div className="grow">
                              <div className="flex items-center">
                                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">UNIDADES CONFIRMADAS</h3>
                                <CalculationButton
                                  kpiId="unidades"
                                  calculation="Unidades confirmadas vs solicitadas por semana"
                                />
                              </div>
                              <div className="flex items-center">
                                <div className="text-xl font-bold text-gray-900">
                                  {indicadoresLogisticaActuales.unidadesConfirmadas} / {indicadoresLogisticaActuales.unidadesSolicitadas}
                                </div>
                                <p className="text-xs text-gray-400 ml-3">
                                  Tasa: {((indicadoresLogisticaActuales.unidadesConfirmadas / indicadoresLogisticaActuales.unidadesSolicitadas) * 100).toFixed(1)}%
                                </p>
                              </div>
                            </div>
                          </div>
                          <div
                            style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                            onClick={() => {
                              // Obtener datos de todas las semanas disponibles según filtro temporal
                              const todasLasSemanas = filteredKpisLogistica.slice().reverse().map((kpi: any) => ({
                                semana: formatWeekDatesForChart(kpi.year, kpi.weekNumber),
                                unidadesConfirmadas: kpi.unidadesConfirmadas,
                                unidadesSolicitadas: kpi.unidadesSolicitadas
                              }));

                              const chartData = {
                                labels: todasLasSemanas.map(item => item.semana),
                                datasets: [
                                  {
                                    label: 'Unidades Confirmadas',
                                    data: todasLasSemanas.map(item => item.unidadesConfirmadas),
                                    backgroundColor: 'rgba(16, 185, 129, 0.8)',
                                    borderColor: '#10b981',
                                    borderWidth: 1,
                                    stack: 'stack1'
                                  },
                                  {
                                    label: 'Unidades No Confirmadas',
                                    data: todasLasSemanas.map(item => Math.max(0, item.unidadesSolicitadas - item.unidadesConfirmadas)),
                                    backgroundColor: 'rgba(239, 68, 68, 0.6)',
                                    borderColor: '#ef4444',
                                    borderWidth: 1,
                                    stack: 'stack1'
                                  }
                                ]
                              };
                              const chartOptions = {
                                layout: getChartLayout(),
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  tooltip: {
                                    callbacks: {
                                      label: (context: any) => `${context.dataset.label}: ${context.parsed.y} unidades`,
                                      afterBody: (tooltipItems: any) => {
                                        const dataIndex = tooltipItems[0].dataIndex;
                                        const semanaData = todasLasSemanas[dataIndex];
                                        return [
                                          `Total Solicitadas: ${semanaData.unidadesSolicitadas} unidades`,
                                          `Total Confirmadas: ${semanaData.unidadesConfirmadas} unidades`,
                                          `Tasa de Confirmación: ${((semanaData.unidadesConfirmadas / semanaData.unidadesSolicitadas) * 100).toFixed(1)}%`
                                        ];
                                      }
                                    }
                                  },
                                  datalabels: getSmartDataLabelsConfig((value: any) => `${value}`)
                                },
                                aspectRatio: 5 / 3,
                                scales: getStackedChartScalesConfig(
                                  'Período',
                                  'Número de Unidades',
                                  (value: any) => `${value} unidades`
                                )
                              };
                              openChartDialog('Unidades Confirmadas vs Solicitadas', chartData, chartOptions, 'bar');
                            }}
                          >
                            <Bar
                              data={{
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [
                                  {
                                    label: 'Unidades Confirmadas',
                                    data: evolucionLogistica.map(item => item.unidadesConfirmadas),
                                    backgroundColor: 'rgba(16, 185, 129, 0.8)',
                                    borderColor: '#10b981',
                                    borderWidth: 1,
                                    stack: 'stack1'
                                  },
                                  {
                                    label: 'Unidades No Confirmadas',
                                    data: evolucionLogistica.map(item => Math.max(0, item.unidadesSolicitadas - item.unidadesConfirmadas)),
                                    backgroundColor: 'rgba(239, 68, 68, 0.6)',
                                    borderColor: '#ef4444',
                                    borderWidth: 1,
                                    stack: 'stack1'
                                  }
                                ]
                              }}
                              options={{
                                responsive: true,
                                maintainAspectRatio: false,
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  legend: { display: false },
                                  tooltip: {
                                    animation: {
                                      duration: 200
                                    },
                                    callbacks: {
                                      label: (context: any) => `${context.dataset.label}: ${context.parsed.y} unidades`,
                                      afterBody: (tooltipItems: any) => {
                                        const dataIndex = tooltipItems[0].dataIndex;
                                        const semanaData = evolucionLogistica[dataIndex];
                                        return [
                                          `Total Solicitadas: ${semanaData.unidadesSolicitadas} unidades`,
                                          `Total Confirmadas: ${semanaData.unidadesConfirmadas} unidades`,
                                          `Tasa de Confirmación: ${((semanaData.unidadesConfirmadas / semanaData.unidadesSolicitadas) * 100).toFixed(1)}%`
                                        ];
                                      }
                                    }
                                  },
                                  datalabels: {
                                    display: false
                                  }
                                },
                                scales: {
                                  x: {
                                    display: false,
                                    stacked: true
                                  },
                                  y: {
                                    display: false,
                                    stacked: true
                                  }
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>

                      {/* 2. Porcentaje de Entregas a Tiempo */}
                      <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                        <div className="p-6">
                          <div className="flex items-center mb-3">
                            <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-green-500/10 flex items-center justify-center mr-3">
                              <TrendingUp className="h-5 w-5 text-green-500" />
                            </div>
                            <div className="grow">
                              <div className="flex items-center">
                                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">ENTREGAS A TIEMPO (%)</h3>
                                <CalculationButton
                                  kpiId="entregas"
                                  calculation="Porcentaje de entregas realizadas dentro del tiempo programado"
                                />
                              </div>
                              <div className="text-xl font-bold text-gray-900">
                                {indicadoresLogisticaActuales.porcentajeEntregasTiempo.toFixed(1)}%
                              </div>
                            </div>
                          </div>
                          <div
                            style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                            onClick={() => {
                              const chartData = {
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [
                                  {
                                    label: 'Entregas a Tiempo (%)',
                                    data: evolucionLogistica.map(item => Number(item.porcentajeEntregasTiempo.toFixed(2))),
                                    borderColor: '#10b981',
                                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                    borderWidth: 3,
                                    fill: false,
                                    tension: 0.4,
                                    pointRadius: 4,
                                    pointHoverRadius: 6,
                                    pointBackgroundColor: '#10b981',
                                    pointBorderColor: '#ffffff',
                                    pointBorderWidth: 2
                                  },
                                  {
                                    label: 'Retardos / Estadías (%)',
                                    data: evolucionLogistica.map(item => Number(item.porcentajeRetardos.toFixed(2))),
                                    borderColor: '#f59e0b',
                                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                                    borderWidth: 3,
                                    fill: false,
                                    tension: 0.4,
                                    pointRadius: 4,
                                    pointHoverRadius: 6,
                                    pointBackgroundColor: '#f59e0b',
                                    pointBorderColor: '#ffffff',
                                    pointBorderWidth: 2
                                  },
                                  {
                                    label: 'Reprogramaciones (%)',
                                    data: evolucionLogistica.map(item => Number(item.porcentajeReprogramaciones.toFixed(2))),
                                    borderColor: '#ef4444',
                                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                                    borderWidth: 3,
                                    fill: false,
                                    tension: 0.4,
                                    pointRadius: 4,
                                    pointHoverRadius: 6,
                                    pointBackgroundColor: '#ef4444',
                                    pointBorderColor: '#ffffff',
                                    pointBorderWidth: 2
                                  }
                                ]
                              };
                              const chartOptions = {
                                layout: getChartLayout(),
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  tooltip: {
                                    callbacks: {
                                      label: (context: any) => `${context.dataset.label}: ${context.parsed.y.toFixed(2)}%`
                                    }
                                  },
                                  datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(1)}%`)
                                },
                                aspectRatio: 5 / 3,
                                scales: {
                                  ...getExpandedChartScalesConfig(
                                    'Semanas',
                                    'Porcentaje (%)',
                                    (value: any) => `${Number(value).toFixed(1)}%`
                                  ),
                                  y: {
                                    ...getExpandedChartScalesConfig('', '', undefined).y,
                                    title: {
                                      display: true,
                                      text: 'Porcentaje (%)'
                                    },
                                    beginAtZero: true,
                                    max: 100,
                                    ticks: {
                                      callback: (value: any) => `${Number(value).toFixed(1)}%`
                                    }
                                  }
                                }
                              };
                              openChartDialog('Entregas a Tiempo, Retardos y Reprogramaciones', chartData, chartOptions, 'line');
                            }}
                          >
                            <Line
                              data={{
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [
                                  {
                                    label: 'Entregas a Tiempo (%)',
                                    data: evolucionLogistica.map(item => Number(item.porcentajeEntregasTiempo.toFixed(2))),
                                    borderColor: '#10b981',
                                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                    borderWidth: 3,
                                    fill: false,
                                    tension: 0.4,
                                    pointRadius: 4,
                                    pointHoverRadius: 6,
                                    pointBackgroundColor: '#10b981',
                                    pointBorderColor: '#ffffff',
                                    pointBorderWidth: 2
                                  },
                                  {
                                    label: 'Retardos / Estadías (%)',
                                    data: evolucionLogistica.map(item => Number(item.porcentajeRetardos.toFixed(2))),
                                    borderColor: '#f59e0b',
                                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                                    borderWidth: 3,
                                    fill: false,
                                    tension: 0.4,
                                    pointRadius: 4,
                                    pointHoverRadius: 6,
                                    pointBackgroundColor: '#f59e0b',
                                    pointBorderColor: '#ffffff',
                                    pointBorderWidth: 2
                                  },
                                  {
                                    label: 'Reprogramaciones (%)',
                                    data: evolucionLogistica.map(item => Number(item.porcentajeReprogramaciones.toFixed(2))),
                                    borderColor: '#ef4444',
                                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                                    borderWidth: 3,
                                    fill: false,
                                    tension: 0.4,
                                    pointRadius: 4,
                                    pointHoverRadius: 6,
                                    pointBackgroundColor: '#ef4444',
                                    pointBorderColor: '#ffffff',
                                    pointBorderWidth: 2
                                  }
                                ]
                              }}
                              options={{
                                responsive: true,
                                maintainAspectRatio: false,
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  legend: { display: false },
                                  tooltip: {
                                    animation: {
                                      duration: 200
                                    },
                                    callbacks: {
                                      label: (context: any) => `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`
                                    }
                                  },
                                  datalabels: {
                                    display: false
                                  }
                                },
                                scales: {
                                  x: { display: false },
                                  y: { display: false }
                                },
                                elements: {
                                  point: { radius: 4 }
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>

                      {/* 3. Promedio de KM por Operación */}
                      <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                        <div className="p-6">
                          <div className="flex items-center mb-3">
                            <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-purple-500/10 flex items-center justify-center mr-3">
                              <Activity className="h-5 w-5 text-purple-500" />
                            </div>
                            <div className="grow">
                              <div className="flex items-center">
                                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">KM POR OPERACIÓN</h3>
                                <CalculationButton
                                  kpiId="km"
                                  calculation="Kilómetros promedio recorridos por cada operación de entrega"
                                />
                              </div>
                              <div className="text-xl font-bold text-gray-900">
                                {indicadoresLogisticaActuales.promedioKmOperacion.toFixed(1)} KM
                              </div>
                            </div>
                          </div>
                          <div
                            style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                            onClick={() => {
                              const chartData = {
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [{
                                  label: 'KM por Operación',
                                  data: evolucionLogistica.map(item => Number(item.promedioKmOperacion.toFixed(2))),
                                  backgroundColor: 'rgba(139, 92, 246, 0.8)',
                                  borderColor: '#8b5cf6',
                                  borderWidth: 1
                                }]
                              };
                              const chartOptions = {
                                layout: getChartLayout(),
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  tooltip: {
                                    callbacks: {
                                      label: (context: any) => `${context.parsed.y.toFixed(1)} KM`
                                    }
                                  },
                                  datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(0)}`)
                                },
                                aspectRatio: 5 / 3,
                                scales: getExpandedChartScalesConfig(
                                  'Semanas',
                                  'Kilómetros',
                                  (value: any) => `${Number(value).toFixed(0)} KM`
                                )
                              };
                              openChartDialog('Promedio de KM Recorridos por Operación', chartData, chartOptions, 'bar');
                            }}
                          >
                            <Bar
                              data={{
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [{
                                  label: 'KM por Operación',
                                  data: evolucionLogistica.map(item => Number(item.promedioKmOperacion.toFixed(2))),
                                  backgroundColor: 'rgba(139, 92, 246, 0.8)',
                                  borderColor: '#8b5cf6',
                                  borderWidth: 1
                                }]
                              }}
                              options={{
                                responsive: true,
                                maintainAspectRatio: false,
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  legend: { display: false },
                                  tooltip: {
                                    animation: {
                                      duration: 200
                                    },
                                    callbacks: {
                                      label: (context: any) => `${context.parsed.y.toFixed(1)} KM`
                                    }
                                  },
                                  datalabels: {
                                    display: false
                                  }
                                },
                                scales: {
                                  x: { display: false },
                                  y: { display: false }
                                },
                                elements: {
                                  point: { radius: 4 }
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Segunda Fila - 1 Columna */}
                    <div className="grid lg:grid-cols-1 sm:grid-cols-1 grid-cols-1 gap-6 my-3">
                      {/* 4. Costo de Flete por Litro */}
                      <div className="bg-white shadow-md border border-gray-200 rounded-lg relative">
                        <div className="p-6 relative">
                          <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-end-8 after:-z-10 after:bg-[url('../assets/images/pattern/dot5.svg')] hidden sm:block" />
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-cyan-500/10 flex items-center justify-center mr-3">
                                <DollarSign className="h-5 w-5 text-cyan-500" />
                              </div>
                              <div>
                                <div className="flex items-center">
                                  <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">COSTO FLETE/LITRO ($)</h3>
                                  <CalculationButton
                                    kpiId="costoLitro"
                                    calculation="Costo promedio de flete por litro transportado"
                                  />
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-xl font-bold text-gray-900">
                                ${indicadoresLogisticaActuales.promedioCostoFleteLitro.toFixed(2)}
                              </div>
                              <p className="text-xs text-gray-400">Costo por litro</p>
                            </div>
                          </div>
                          <div
                            style={{ width: '100%', height: '156px', cursor: 'pointer' }}
                            onClick={() => {
                              const chartData = {
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [{
                                  label: 'Costo por Litro ($)',
                                  data: evolucionLogistica.map(item => Number(item.promedioCostoFleteLitro.toFixed(2))),
                                  borderColor: '#06b6d4',
                                  backgroundColor: 'rgba(6, 182, 212, 0.1)',
                                  borderWidth: 3,
                                  fill: true,
                                  tension: 0.4,
                                  pointRadius: 4,
                                  pointHoverRadius: 6,
                                  pointBackgroundColor: '#06b6d4',
                                  pointBorderColor: '#ffffff',
                                  pointBorderWidth: 2
                                }]
                              };
                              const chartOptions = {
                                layout: getChartLayout(),
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  tooltip: {
                                    callbacks: {
                                      label: (context: any) => `$${context.parsed.y.toFixed(2)} por litro`
                                    }
                                  },
                                  datalabels: getSmartDataLabelsConfig((value: any) => `$${Number(value).toFixed(2)}`)
                                },
                                aspectRatio: 5 / 3,
                                scales: {
                                  x: {
                                    display: true,
                                    title: {
                                      display: true,
                                      text: 'Período'
                                    }
                                  },
                                  y: {
                                    display: true,
                                    title: {
                                      display: true,
                                      text: 'Costos en Pesos ($)'
                                    },
                                    ticks: {
                                      callback: (value: any) => `$${Number(value).toFixed(2)}`
                                    }
                                  }
                                }
                              };
                              openChartDialog('Promedio de Costo de Flete por Litro', chartData, chartOptions, 'line');
                            }}
                          >
                            <Line
                              data={{
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [{
                                  label: 'Costo por Litro ($)',
                                  data: evolucionLogistica.map(item => Number(item.promedioCostoFleteLitro.toFixed(2))),
                                  borderColor: '#06b6d4',
                                  backgroundColor: 'rgba(6, 182, 212, 0.1)',
                                  borderWidth: 3,
                                  fill: true,
                                  tension: 0.4,
                                  pointRadius: 4,
                                  pointHoverRadius: 6,
                                  pointBackgroundColor: '#06b6d4',
                                  pointBorderColor: '#ffffff',
                                  pointBorderWidth: 2
                                }]
                              }}
                              options={{
                                responsive: true,
                                maintainAspectRatio: false,
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  legend: { display: false },
                                  tooltip: {
                                    animation: {
                                      duration: 200
                                    },
                                    callbacks: {
                                      label: (context: any) => `$${context.parsed.y.toFixed(2)} por litro`
                                    }
                                  },
                                  datalabels: {
                                    display: false
                                  }
                                },
                                scales: {
                                  x: { display: false },
                                  y: { display: false }
                                },
                                elements: {
                                  point: { radius: 4 }
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Tercera Fila - 3 Columnas */}
                    <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6 my-3">
                      {/* 5. Costo de Flete por Operación */}
                      <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                        <div className="p-6">
                          <div className="flex items-center mb-3">
                            <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-lime-500/10 flex items-center justify-center mr-3">
                              <Truck className="h-5 w-5 text-lime-500" />
                            </div>
                            <div className="grow">
                              <div className="flex items-center">
                                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">COSTO FLETE/OPERACIÓN ($)</h3>
                                <CalculationButton
                                  kpiId="costoOperacion"
                                  calculation="Costo promedio de flete por operación completa"
                                />
                              </div>
                              <div className="text-xl font-bold text-gray-900">
                                ${indicadoresLogisticaActuales.promedioCostoFleteOperacion.toLocaleString()}
                              </div>
                            </div>
                          </div>
                          <div
                            style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                            onClick={() => {
                              const chartData = {
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [
                                  {
                                    label: 'Costo por Operación ($)',
                                    data: evolucionLogistica.map(item => Number(item.promedioCostoFleteOperacion.toFixed(2))),
                                    borderColor: '#84cc16',
                                    borderWidth: 3,
                                    fill: false,
                                    tension: 0.4,
                                    pointRadius: 4,
                                    pointHoverRadius: 6,
                                    pointBackgroundColor: '#84cc16',
                                    pointBorderColor: '#ffffff',
                                    pointBorderWidth: 2
                                  },
                                  {
                                    label: 'Promedio General',
                                    data: evolucionLogistica.map(() => {
                                      const promedio = evolucionLogistica.reduce((sum, item) => sum + item.promedioCostoFleteOperacion, 0) / evolucionLogistica.length;
                                      return Number(promedio.toFixed(2));
                                    }),
                                    borderColor: '#ef4444',
                                    borderWidth: 2,
                                    borderDash: [5, 5],
                                    fill: false,
                                    pointRadius: 0,
                                    pointHoverRadius: 0,
                                    tension: 0,
                                    datalabels: {
                                      display: function(context: any) {
                                        return context.dataIndex === Math.floor(context.dataset.data.length / 2); // Solo mostrar en el punto medio
                                      },
                                      align: 'top',
                                      backgroundColor: '#ef4444',
                                      borderRadius: 8,
                                      color: 'white',
                                      font: { weight: 'bold' },
                                      padding: 6,
                                      formatter: (value: any) => `$${Number(value).toFixed(0)}`
                                    }
                                  }
                                ]
                              };
                              const chartOptions = {
                                layout: getChartLayout(),
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  tooltip: {
                                    callbacks: {
                                      label: (context: any) => `$${context.parsed.y.toFixed(2)} por operación`
                                    }
                                  },
                                  datalabels: getSmartDataLabelsConfig((value: any) => `$${Number(value).toFixed(0)}`)
                                },
                                aspectRatio: 5 / 3,
                                scales: {
                                  x: {
                                    display: true,
                                    title: {
                                      display: true,
                                      text: 'Período'
                                    }
                                  },
                                  y: {
                                    display: true,
                                    title: {
                                      display: true,
                                      text: 'Costos en Pesos ($)'
                                    },
                                    ticks: {
                                      callback: (value: any) => `$${Number(value).toFixed(0)}`
                                    }
                                  }
                                }
                              };
                              openChartDialog('Promedio de Costo de Flete por Operación', chartData, chartOptions, 'line');
                            }}
                          >
                            <Line
                              data={{
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [
                                  {
                                    label: 'Costo por Operación ($)',
                                    data: evolucionLogistica.map(item => Number(item.promedioCostoFleteOperacion.toFixed(2))),
                                    borderColor: '#84cc16',
                                    borderWidth: 3,
                                    fill: false,
                                    tension: 0.4,
                                    pointRadius: 4,
                                    pointHoverRadius: 6,
                                    pointBackgroundColor: '#84cc16',
                                    pointBorderColor: '#ffffff',
                                    pointBorderWidth: 2
                                  },
                                  {
                                    label: 'Promedio General',
                                    data: evolucionLogistica.map(() => {
                                      const promedio = evolucionLogistica.reduce((sum, item) => sum + item.promedioCostoFleteOperacion, 0) / evolucionLogistica.length;
                                      return Number(promedio.toFixed(2));
                                    }),
                                    borderColor: '#ef4444',
                                    borderWidth: 2,
                                    borderDash: [5, 5],
                                    fill: false,
                                    pointRadius: 0,
                                    pointHoverRadius: 0,
                                    tension: 0,
                                    datalabels: {
                                      display: false // No mostrar labels en la gráfica pequeña
                                    }
                                  }
                                ]
                              }}
                              options={{
                                responsive: true,
                                maintainAspectRatio: false,
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  legend: { display: false },
                                  tooltip: {
                                    animation: {
                                      duration: 200
                                    },
                                    callbacks: {
                                      label: (context: any) => `$${context.parsed.y.toFixed(0)} por operación`
                                    }
                                  },
                                  datalabels: {
                                    display: false
                                  }
                                },
                                scales: {
                                  x: { display: false },
                                  y: { display: false }
                                },
                                elements: {
                                  point: { radius: 4 }
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>

                      {/* 6. Pago Semanal de Flete */}
                      <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                        <div className="p-6">
                          <div className="flex items-center mb-3">
                            <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-indigo-500/10 flex items-center justify-center mr-3">
                              <Banknote className="h-5 w-5 text-indigo-500" />
                            </div>
                            <div className="grow">
                              <div className="flex items-center">
                                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">PAGO SEMANAL FLETE ($)</h3>
                                <CalculationButton
                                  kpiId="pagoFlete"
                                  calculation="Monto total pagado por concepto de flete durante la semana"
                                />
                              </div>
                              <div className="text-xl font-bold text-gray-900">
                                ${(indicadoresLogisticaActuales.pagoSemanalFlete / 1000).toFixed(0)}K
                              </div>
                            </div>
                          </div>
                          <div
                            style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                            onClick={() => {
                              // Obtener datos de todas las semanas disponibles según filtro temporal
                              const todasLasSemanasFletes = filteredKpisLogistica.slice().reverse().map((kpi: any) => ({
                                semana: formatWeekDatesForChart(kpi.year, kpi.weekNumber),
                                pagoSemanalFlete: Number(kpi.pagoSemanalFlete.toFixed(2))
                              }));

                              const chartData = {
                                labels: todasLasSemanasFletes.map(item => item.semana),
                                datasets: [{
                                  label: 'Pago Semanal de Flete ($)',
                                  data: todasLasSemanasFletes.map(item => item.pagoSemanalFlete),
                                  backgroundColor: 'rgba(99, 102, 241, 0.1)',
                                  borderColor: '#6366f1',
                                  borderWidth: 3,
                                  fill: true,
                                  tension: 0.4,
                                  pointRadius: 4,
                                  pointHoverRadius: 6,
                                  pointBackgroundColor: '#6366f1',
                                  pointBorderColor: '#ffffff',
                                  pointBorderWidth: 2
                                }]
                              };
                              const chartOptions = {
                                layout: getChartLayout(),
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  tooltip: {
                                    callbacks: {
                                      label: (context: any) => `$${context.parsed.y.toFixed(0)}`
                                    }
                                  },
                                  datalabels: getSmartDataLabelsConfig((value: any) => `$${(Number(value) / 1000).toFixed(0)}K`)
                                },
                                aspectRatio: 5 / 3,
                                scales: getExpandedChartScalesConfig(
                                  'Semanas',
                                  'Monto en Pesos ($)',
                                  (value: any) => `$${(Number(value) / 1000).toFixed(0)}K`
                                )
                              };
                              openChartDialog('Pago Semanal de Flete', chartData, chartOptions, 'line');
                            }}
                          >
                            <Line
                              data={{
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [{
                                  label: 'Pago Semanal de Flete ($)',
                                  data: evolucionLogistica.map(item => Number(item.pagoSemanalFlete.toFixed(2))),
                                  backgroundColor: 'rgba(99, 102, 241, 0.1)',
                                  borderColor: '#6366f1',
                                  borderWidth: 3,
                                  fill: true,
                                  tension: 0.4,
                                  pointRadius: 4,
                                  pointHoverRadius: 6,
                                  pointBackgroundColor: '#6366f1',
                                  pointBorderColor: '#ffffff',
                                  pointBorderWidth: 2
                                }]
                              }}
                              options={{
                                responsive: true,
                                maintainAspectRatio: false,
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  legend: { display: false },
                                  tooltip: {
                                    animation: {
                                      duration: 200
                                    },
                                    callbacks: {
                                      label: (context: any) => `$${(context.parsed.y / 1000).toFixed(0)}K`
                                    }
                                  },
                                  datalabels: {
                                    display: false
                                  }
                                },
                                scales: {
                                  x: { display: false },
                                  y: { display: false }
                                },
                                elements: {
                                  point: { radius: 4 }
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>

                      {/* 7. Pago Semanal por Penalizaciones */}
                      <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                        <div className="p-6">
                          <div className="flex items-center mb-3">
                            <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-red-500/10 flex items-center justify-center mr-3">
                              <Trash2 className="h-5 w-5 text-red-500" />
                            </div>
                            <div className="grow">
                              <div className="flex items-center">
                                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">PENALIZACIONES ($)</h3>
                                <CalculationButton
                                  kpiId="penalizaciones"
                                  calculation="Monto total pagado por penalizaciones durante la semana"
                                />
                              </div>
                              <div className="text-xl font-bold text-gray-900">
                                ${(indicadoresLogisticaActuales.pagoSemanalPenalizaciones / 1000).toFixed(0)}K
                              </div>
                            </div>
                          </div>
                          <div
                            style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                            onClick={() => {
                              // Obtener datos de todas las semanas disponibles según filtro temporal
                              const todasLasSemanasP = filteredKpisLogistica.slice().reverse().map((kpi: any) => ({
                                semana: formatWeekDatesForChart(kpi.year, kpi.weekNumber),
                                pagoSemanalPenalizaciones: Number(kpi.pagoSemanalPenalizaciones.toFixed(2))
                              }));

                              const chartData = {
                                labels: todasLasSemanasP.map(item => item.semana),
                                datasets: [{
                                  label: 'Pago Semanal por Penalizaciones ($)',
                                  data: todasLasSemanasP.map(item => item.pagoSemanalPenalizaciones),
                                  borderColor: '#ef4444',
                                  borderWidth: 3,
                                  fill: false,
                                  tension: 0.4,
                                  pointRadius: 4,
                                  pointHoverRadius: 6,
                                  pointBackgroundColor: '#ef4444',
                                  pointBorderColor: '#ffffff',
                                  pointBorderWidth: 2
                                }]
                              };
                              const chartOptions = {
                                layout: getChartLayout(),
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  tooltip: {
                                    callbacks: {
                                      label: (context: any) => `$${context.parsed.y.toFixed(0)}`
                                    }
                                  },
                                  datalabels: getSmartDataLabelsConfig((value: any) => `$${(Number(value) / 1000).toFixed(0)}K`)
                                },
                                aspectRatio: 5 / 3,
                                scales: getExpandedChartScalesConfig(
                                  'Semanas',
                                  'Monto en Pesos ($)',
                                  (value: any) => `$${(Number(value) / 1000).toFixed(0)}K`
                                )
                              };
                              openChartDialog('Pago Semanal por Penalizaciones', chartData, chartOptions, 'line');
                            }}
                          >
                            <Line
                              data={{
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [{
                                  label: 'Pago por Penalizaciones ($)',
                                  data: evolucionLogistica.map(item => Number(item.pagoSemanalPenalizaciones.toFixed(2))),
                                  borderColor: '#ef4444',
                                  borderWidth: 3,
                                  fill: false,
                                  tension: 0.4,
                                  pointRadius: 4,
                                  pointHoverRadius: 6,
                                  pointBackgroundColor: '#ef4444',
                                  pointBorderColor: '#ffffff',
                                  pointBorderWidth: 2
                                }]
                              }}
                              options={{
                                responsive: true,
                                maintainAspectRatio: false,
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  legend: { display: false },
                                  tooltip: {
                                    animation: {
                                      duration: 200
                                    },
                                    callbacks: {
                                      label: (context: any) => `$${(context.parsed.y / 1000).toFixed(0)}K`
                                    }
                                  },
                                  datalabels: {
                                    display: false
                                  }
                                },
                                scales: {
                                  x: { display: false },
                                  y: { display: false }
                                },
                                elements: {
                                  point: { radius: 4 }
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Cuarta Fila - 2 Columnas */}
                    <div className="grid lg:grid-cols-2 sm:grid-cols-2 grid-cols-1 gap-6 my-3">


                      {/* 8. Porcentaje de Rutas Cotizadas a Tiempo */}
                      <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                        <div className="p-6">
                          <div className="flex items-center mb-3">
                            <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-orange-500/10 flex items-center justify-center mr-3">
                              <Target className="h-5 w-5 text-orange-500" />
                            </div>
                            <div className="grow">
                              <div className="flex items-center">
                                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">RUTAS COTIZADAS (%)</h3>
                                <CalculationButton
                                  kpiId="rutas"
                                  calculation="Porcentaje de rutas que fueron cotizadas dentro del tiempo establecido"
                                />
                              </div>
                              <div className="text-xl font-bold text-gray-900">
                                {indicadoresLogisticaActuales.porcentajeRutasCotizadas.toFixed(1)}%
                              </div>
                            </div>
                          </div>
                          <div
                            style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                            onClick={() => {
                              const chartData = {
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [{
                                  label: 'Rutas Cotizadas a Tiempo (%)',
                                  data: evolucionLogistica.map(item => Number(item.porcentajeRutasCotizadas.toFixed(2))),
                                  backgroundColor: 'rgba(249, 115, 22, 0.8)',
                                  borderColor: '#f97316',
                                  borderWidth: 1
                                }]
                              };
                              const chartOptions = {
                                layout: getChartLayout(),
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  tooltip: {
                                    callbacks: {
                                      label: (context: any) => `${context.parsed.y.toFixed(1)}%`
                                    }
                                  },
                                  datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(1)}%`)
                                },
                                aspectRatio: 5 / 3,
                                scales: {
                                  x: {
                                    display: true,
                                    title: {
                                      display: true,
                                      text: 'Período'
                                    }
                                  },
                                  y: {
                                    display: true,
                                    title: {
                                      display: true,
                                      text: 'Porcentaje (%)'
                                    },
                                    ticks: {
                                      callback: (value: any) => `${Number(value).toFixed(1)}%`
                                    }
                                  }
                                }
                              };
                              openChartDialog('Porcentaje de Rutas Cotizadas a Tiempo', chartData, chartOptions, 'bar');
                            }}
                          >
                            <Bar
                              data={{
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [{
                                  label: 'Rutas Cotizadas a Tiempo (%)',
                                  data: evolucionLogistica.map(item => Number(item.porcentajeRutasCotizadas.toFixed(2))),
                                  backgroundColor: 'rgba(249, 115, 22, 0.8)',
                                  borderColor: '#f97316',
                                  borderWidth: 1
                                }]
                              }}
                              options={{
                                responsive: true,
                                maintainAspectRatio: false,
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  legend: { display: false },
                                  tooltip: {
                                    animation: {
                                      duration: 200
                                    },
                                    callbacks: {
                                      label: (context: any) => `${context.parsed.y.toFixed(1)}%`
                                    }
                                  },
                                  datalabels: {
                                    display: false
                                  }
                                },
                                scales: {
                                  x: { display: false },
                                  y: { display: false }
                                },
                                elements: {
                                  point: { radius: 4 }
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>

                      {/* 9. Porcentaje de Transportistas Clasificados */}
                      <div className="bg-white shadow-md border border-gray-200 rounded-lg">
                        <div className="p-6">
                          <div className="flex items-center mb-3">
                            <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-teal-500/10 flex items-center justify-center mr-3">
                              <Users className="h-5 w-5 text-teal-500" />
                            </div>
                            <div className="grow">
                              <div className="flex items-center">
                                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">TRANSPORTISTAS (%)</h3>
                                <CalculationButton
                                  kpiId="transportistas"
                                  calculation="Porcentaje de transportistas que han sido clasificados según criterios de desempeño"
                                />
                              </div>
                              <div className="text-xl font-bold text-gray-900">
                                {indicadoresLogisticaActuales.porcentajeTransportistas.toFixed(1)}%
                              </div>
                            </div>
                          </div>
                          <div
                            style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                            onClick={() => {
                              const chartData = {
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [{
                                  label: 'Transportistas Clasificados (%)',
                                  data: evolucionLogistica.map(item => Number(item.porcentajeTransportistas.toFixed(2))),
                                  borderColor: '#14b8a6',
                                  borderWidth: 3,
                                  fill: false,
                                  tension: 0.4,
                                  pointRadius: 4,
                                  pointHoverRadius: 6,
                                  pointBackgroundColor: '#14b8a6',
                                  pointBorderColor: '#ffffff',
                                  pointBorderWidth: 2
                                }]
                              };
                              const chartOptions = {
                                layout: getChartLayout(),
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  tooltip: {
                                    callbacks: {
                                      label: (context: any) => `${context.parsed.y.toFixed(1)}%`
                                    }
                                  },
                                  datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(1)}%`)
                                },
                                aspectRatio: 5 / 3,
                                scales: {
                                  x: {
                                    display: true,
                                    title: {
                                      display: true,
                                      text: 'Período'
                                    }
                                  },
                                  y: {
                                    display: true,
                                    title: {
                                      display: true,
                                      text: 'Porcentaje (%)'
                                    },
                                    ticks: {
                                      callback: (value: any) => `${Number(value).toFixed(1)}%`
                                    }
                                  }
                                }
                              };
                              openChartDialog('Porcentaje de Transportistas Clasificados', chartData, chartOptions, 'line');
                            }}
                          >
                            <Line
                              data={{
                                labels: evolucionLogistica.map(item => item.mes),
                                datasets: [{
                                  label: 'Transportistas Clasificados (%)',
                                  data: evolucionLogistica.map(item => Number(item.porcentajeTransportistas.toFixed(2))),
                                  borderColor: '#14b8a6',
                                  borderWidth: 3,
                                  fill: false,
                                  tension: 0.4,
                                  pointRadius: 4,
                                  pointHoverRadius: 6,
                                  pointBackgroundColor: '#14b8a6',
                                  pointBorderColor: '#ffffff',
                                  pointBorderWidth: 2
                                }]
                              }}
                              options={{
                                responsive: true,
                                maintainAspectRatio: false,
                                animation: {
                                  duration: 1000,
                                  easing: 'easeInOutQuart'
                                },
                                interaction: {
                                  intersect: false,
                                  mode: 'nearest'
                                },
                                plugins: {
                                  legend: { display: false },
                                  tooltip: {
                                    animation: {
                                      duration: 200
                                    },
                                    callbacks: {
                                      label: (context: any) => `${context.parsed.y.toFixed(1)}%`
                                    }
                                  },
                                  datalabels: {
                                    display: false
                                  }
                                },
                                scales: {
                                  x: { display: false },
                                  y: { display: false }
                                },
                                elements: {
                                  point: { radius: 4 }
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Gráficas Expandidas - Solo se muestran cuando showAnalyticsLogistica es true */}
                    {showAnalyticsLogistica && (
                      <div className="mt-8">
                        <div className="mb-6">
                          <h2 className="text-lg font-semibold text-gray-900 mb-2">Análisis Detallado</h2>
                          <p className="text-sm text-gray-600">Gráficas expandidas para análisis profundo de los KPIs de logística</p>
                        </div>

                        {/* Aquí irían las gráficas expandidas cuando se implementen */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                          <div className="text-center py-8">
                            <div className="h-12 w-12 rounded-md bg-blue-500/10 flex items-center justify-center mx-auto mb-4">
                              <Truck className="h-6 w-6 text-blue-500" />
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">Gráficas Expandidas</h3>
                            <p className="text-gray-500">Las gráficas expandidas para análisis detallado estarán disponibles próximamente.</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                );
              })()}

                </>
              )}
            </div>
          )}

          {activeTab === "contabilidad" && (
            <div className="space-y-10">
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 relative">
                <div className="relative">
                  <div className="before:w-20 before:h-20 before:absolute before:-bottom-12 before:-start-12 before:-z-10 before:bg-[url('../assets/images/pattern/dot2.svg')] hidden sm:block" />
                  <div className="text-center">
                    <span className="text-xs md:text-xs lg:text-sm font-medium py-1 px-3 rounded-full text-primary bg-primary/10">
                      Gestión Financiera
                    </span>
                    <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-3 mb-4 max-w-2xl justify-center mx-auto">
                      Panel de Control de Contabilidad
                    </h1>
                    <p className="text-base md:text-base lg:text-xl text-gray-500">
                      Administración contable y control financiero
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="h-12 w-12 rounded-md bg-green-500/10 flex items-center justify-center mb-4">
                  <Calculator className="h-6 w-6 text-green-500" />
                </div>
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-4">Panel de Contabilidad</h3>
                <p className="text-base md:text-base lg:text-lg text-gray-500">Funcionalidad de contabilidad en desarrollo...</p>
              </div>
            </div>
          )}

          {activeTab === "operaciones" && (
            <div className="space-y-10">
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 relative">
                <div className="relative">
                  <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-end-8 after:-z-10 after:bg-[url('../assets/images/pattern/dot5.svg')] hidden sm:block" />
                  <div className="text-center">
                    <span className="text-xs md:text-xs lg:text-sm font-medium py-1 px-3 rounded-full text-primary bg-primary/10">
                      Gestión Operativa
                    </span>
                    <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-3 mb-4 max-w-2xl justify-center mx-auto">
                      Panel de Control de Operaciones
                    </h1>
                    <p className="text-base md:text-base lg:text-xl text-gray-500">
                      Administración de operaciones diarias y procesos
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="h-12 w-12 rounded-md bg-purple-500/10 flex items-center justify-center mb-4">
                  <Settings className="h-6 w-6 text-purple-500" />
                </div>
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-4">Panel de Operaciones</h3>
                <p className="text-base md:text-base lg:text-lg text-gray-500">Funcionalidad de operaciones en desarrollo...</p>
              </div>
            </div>
          )}

          {activeTab === "legal" && (
            <div className="space-y-10">
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 relative">
                <div className="relative">
                  <div className="before:w-20 before:h-20 before:absolute before:-bottom-12 before:-start-12 before:-z-10 before:bg-[url('../assets/images/pattern/dot2.svg')] hidden sm:block" />
                  <div className="text-center">
                    <span className="text-xs md:text-xs lg:text-sm font-medium py-1 px-3 rounded-full text-primary bg-primary/10">
                      Gestión Jurídica
                    </span>
                    <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-3 mb-4 max-w-2xl justify-center mx-auto">
                      Panel de Control Legal
                    </h1>
                    <p className="text-base md:text-base lg:text-xl text-gray-500">
                      Administración de asuntos legales y cumplimiento normativo
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="h-12 w-12 rounded-md bg-red-500/10 flex items-center justify-center mb-4">
                  <Scale className="h-6 w-6 text-red-500" />
                </div>
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-4">Panel Legal</h3>
                <p className="text-base md:text-base lg:text-lg text-gray-500">Funcionalidad legal en desarrollo...</p>
              </div>
            </div>
          )}

          {activeTab === "planeacion" && (
            <div className="space-y-10">
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 relative">
                <div className="relative">
                  <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-end-8 after:-z-10 after:bg-[url('../assets/images/pattern/dot5.svg')] hidden sm:block" />
                  <div className="text-center">
                    <span className="text-xs md:text-xs lg:text-sm font-medium py-1 px-3 rounded-full text-primary bg-primary/10">
                      Gestión Estratégica
                    </span>
                    <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-3 mb-4 max-w-2xl justify-center mx-auto">
                      Panel de Control de Planeación
                    </h1>
                    <p className="text-base md:text-base lg:text-xl text-gray-500">
                      Administración de planeación estratégica y proyectos
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="h-12 w-12 rounded-md bg-indigo-500/10 flex items-center justify-center mb-4">
                  <Target className="h-6 w-6 text-indigo-500" />
                </div>
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-4">Panel de Planeación</h3>
                <p className="text-base md:text-base lg:text-lg text-gray-500">Funcionalidad de planeación en desarrollo...</p>
              </div>
            </div>
          )}

          {activeTab === "finanzas" && (
            <div className="space-y-10">
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 relative">
                <div className="relative">
                  <div className="before:w-20 before:h-20 before:absolute before:-bottom-12 before:-start-12 before:-z-10 before:bg-[url('../assets/images/pattern/dot2.svg')] hidden sm:block" />
                  <div className="text-center">
                    <span className="text-xs md:text-xs lg:text-sm font-medium py-1 px-3 rounded-full text-primary bg-primary/10">
                      Gestión Financiera
                    </span>
                    <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-3 mb-4 max-w-2xl justify-center mx-auto">
                      Panel de Control de Finanzas
                    </h1>
                    <p className="text-base md:text-base lg:text-xl text-gray-500">
                      Administración financiera y control de presupuestos
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="h-12 w-12 rounded-md bg-emerald-500/10 flex items-center justify-center mb-4">
                  <Banknote className="h-6 w-6 text-emerald-500" />
                </div>
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-4">Panel de Finanzas</h3>
                <p className="text-base md:text-base lg:text-lg text-gray-500">Funcionalidad de finanzas en desarrollo...</p>
              </div>
            </div>
          )}
            </div>
          </div>
        </div>
      </div>
      </div>

      {/* Modal para captura de KPIs semanales */}
      <KpiSemanalModal
        isOpen={isKpiModalOpen}
        onClose={() => {
          setIsKpiModalOpen(false);
          setEditingKpi(null);
          setIsAddingOldWeek(false);
        }}
        onSave={handleSaveKpi}
        editingKpi={editingKpi}
        isAddingOldWeek={isAddingOldWeek}
        existingKpis={kpisSemanales}
      />

      {/* Modal para captura de KPIs de logística */}
      <KpiLogisticaModal
        isOpen={showKpiLogisticaModal}
        onClose={() => {
          setShowKpiLogisticaModal(false);
          setEditingKpiLogistica(null);
          setIsAddingOldWeekLogistica(false);
        }}
        onSave={handleSaveKpiLogistica}
        editingKpi={editingKpiLogistica}
        isAddingOldWeek={isAddingOldWeekLogistica}
        existingKpis={kpisLogistica}
      />

      {/* Dialog para mostrar gráficas en pantalla completa */}
      <div>

      </div>
      {chartDialog.isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="w-full max-w-[60rem] max-h-[85vh] mx-auto">
            {/* Overlay */}
            <div
              className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
              onClick={() => setChartDialog(prev => ({ ...prev, isOpen: true }))}
            ></div>

            {/* Dialog Content */}
            <div className="relative bg-white rounded-xl shadow-2xl w-full h-auto flex flex-col">
              {/* Header del Dialog */}
              <div className="bg-white px-6 pt-4 rounded-t-xl flex-shrink-0">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-medium text-gray-600">{chartDialog.title}</h2>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => downloadChartAsPNG(chartDialog.title)}
                      className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                      title="Descargar gráfica como PNG"
                    >
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                    </button>
                    <button
                      onClick={() => setChartDialog(prev => ({ ...prev, isOpen: false }))}
                      className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                      title="Cerrar"
                    >
                      <X className="h-6 w-6" />
                    </button>
                  </div>
                </div>
              </div>
              {/* Contenido del gráfico */}
              <div className="bg-white">
                <div id="chart-dialog" className="w-full h-[500px] bg-white">
                  {chartDialog.chartType === 'line' && (
                    <Line
                      data={chartDialog.chartData}
                      options={chartDialog.chartOptions}
                    />
                  )}
                  {chartDialog.chartType === 'pie' && (
                    <Pie
                      data={chartDialog.chartData}
                      options={chartDialog.chartOptions}
                    />
                  )}
                  {chartDialog.chartType === 'doughnut' && (
                    <Doughnut
                      data={chartDialog.chartData}
                      options={chartDialog.chartOptions}
                    />
                  )}
                  {chartDialog.chartType === 'bar' && (
                    <Bar
                      data={chartDialog.chartData}
                      options={chartDialog.chartOptions}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Dialog de confirmación para eliminar */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        message={confirmDialog.message}
        confirmText="Eliminar"
        cancelText="Cancelar"
        type="danger"
        loading={confirmDialog.loading}
      />
    </div>
  );
};

export default AdminDashboard;
